using KPA.Services.QuestPDF;
using KPA.Services.QuestPDF.Services;

namespace KPA.Services.QuestPDF;

/// <summary>
/// Simple test class to verify PDF generation functionality
/// </summary>
public static class TestPdfGeneration
{
    public static void TestSingleImageLayout()
    {
        var project = new WebProject
        {
            Title = "Modern Office Building",
            Description = "A state-of-the-art office building featuring sustainable design elements and modern amenities. The building incorporates green technology and energy-efficient systems throughout its structure.",
            Location = "Downtown Seattle, WA",
            Client = "Tech Corp Inc.",
            ProjectDate = new DateTime(2024, 1, 15),
            ProjectType = "Commercial Architecture",
            ImageUrls = new List<string>
            {
                "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800"
            }
        };

        var pdfService = new WebProjectPdfGeneratorService();
        
        try
        {
            var pdfBytes = pdfService.GeneratePdf(project);
            Console.WriteLine($"✅ Single image PDF generated successfully. Size: {pdfBytes.Length} bytes");
            
            // Save to file for testing
            File.WriteAllBytes("test_single_image.pdf", pdfBytes);
            Console.WriteLine("📄 PDF saved as 'test_single_image.pdf'");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error generating single image PDF: {ex.Message}");
        }
    }

    public static void TestMultipleImagesLayout()
    {
        var project = new WebProject
        {
            Title = "Luxury Resort Complex",
            Description = "A comprehensive resort development featuring multiple buildings, recreational facilities, and landscaped areas. The project combines modern architecture with natural elements to create a harmonious environment for guests.",
            Location = "Malibu, CA",
            Client = "Resort Development Group",
            ProjectDate = new DateTime(2024, 3, 15),
            ProjectType = "Hospitality Architecture",
            ImageUrls = new List<string>
            {
                "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800",
                "https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=600",
                "https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=600",
                "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=600",
                "https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=600"
            }
        };

        var pdfService = new WebProjectPdfGeneratorService();
        
        try
        {
            var pdfBytes = pdfService.GeneratePdf(project);
            Console.WriteLine($"✅ Multiple images PDF generated successfully. Size: {pdfBytes.Length} bytes");
            
            // Save to file for testing
            File.WriteAllBytes("test_multiple_images.pdf", pdfBytes);
            Console.WriteLine("📄 PDF saved as 'test_multiple_images.pdf'");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error generating multiple images PDF: {ex.Message}");
        }
    }

    public static void TestCompanionPreview()
    {
        var project = new WebProject
        {
            Title = "Test Preview Project",
            Description = "This is a test project to verify the QuestPDF Companion preview functionality.",
            Location = "Test Location",
            Client = "Test Client",
            ProjectDate = DateTime.Now,
            ProjectType = "Test Type",
            ImageUrls = new List<string>
            {
                "https://images.unsplash.com/photo-1497366216548-37526070297c?w=800",
                "https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=600"
            }
        };

        var pdfService = new WebProjectPdfGeneratorService();
        
        try
        {
            pdfService.ShowInCompanion(project);
            Console.WriteLine("✅ PDF opened in QuestPDF Companion successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error opening PDF in Companion: {ex.Message}");
            Console.WriteLine("💡 Make sure QuestPDF Companion is installed and running");
        }
    }

    public static void RunAllTests()
    {
        Console.WriteLine("🚀 Starting PDF Generation Tests...\n");
        
        Console.WriteLine("1️⃣ Testing Single Image Layout:");
        TestSingleImageLayout();
        Console.WriteLine();
        
        Console.WriteLine("2️⃣ Testing Multiple Images Layout:");
        TestMultipleImagesLayout();
        Console.WriteLine();
        
        Console.WriteLine("3️⃣ Testing Companion Preview:");
        TestCompanionPreview();
        Console.WriteLine();
        
        Console.WriteLine("✨ All tests completed!");
    }
}
