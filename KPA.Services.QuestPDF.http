@KPA.Services.QuestPDF_HostAddress = http://localhost:5246

### Generate WebProject PDF and upload to Azure Blob Storage
POST {{KPA.Services.QuestPDF_HostAddress}}/api/webproject/pdf/generate-and-upload
Content-Type: application/json

{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "aviara_2024.pdf",
    "title": "AVIARA",
    "typology": "HOUSING | HIGHRISE",
    "location": "MALAD | MUMBAI",
    "status": "ONGOING",
    "client": "ANCHOR POINT DEVELOPERS",
    "area": "70000 SQ M",
    "year": "2024",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/01-Aviara-Semi-Aerial-Front-View.jpg",
                       "orderFlag": 1
                  },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/02-Aviara-Front-View.jpg",
                      "orderFlag": 2
                 },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/03-Aviara-Semi-Aerial-Front-Side-Night-View.jpg",
                      "orderFlag": 3
                  },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/04-Aviara-Commercial-Area.jpg",
                      "orderFlag": 4
                  },
        {
                       "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/05-Aviara-Pool-to-Outer.jpg",
                       "orderFlag": 5
                  },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/Aviara-1.jpeg",
                       "orderFlag": 6
                  },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/Aviara-2.jpeg",
                       "orderFlag": 7
                  }
    ]
   }

###

### Generate WebProject PDF and preview in QuestPDF Companion (Single Image Layout)
POST {{KPA.Services.QuestPDF_HostAddress}}/api/webproject/pdf/generate-and-preview
Content-Type: application/json

{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "aviara_2024.pdf",
    "title": "AVIARA",
    "typology": "HOUSING | HIGHRISE",
    "location": "MALAD | MUMBAI",
    "status": "ONGOING",
    "client": "ANCHOR POINT DEVELOPERS",
    "area": "70000 SQ M",
    "year": "2024",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/01-Aviara-Semi-Aerial-Front-View.jpg",
                       "orderFlag": 1
                  },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/02-Aviara-Front-View.jpg",
                      "orderFlag": 2
                 },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/03-Aviara-Semi-Aerial-Front-Side-Night-View.jpg",
                      "orderFlag": 3
                  },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/04-Aviara-Commercial-Area.jpg",
                      "orderFlag": 4
                  },
        {
                       "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/05-Aviara-Pool-to-Outer.jpg",
                       "orderFlag": 5
                  },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/Aviara-1.jpeg",
                       "orderFlag": 6
                  },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/Aviara-2.jpeg",
                       "orderFlag": 7
                  }
    ]
   }

###

### Generate WebProject PDF and preview (Multiple Images Layout - 5 images)
POST {{KPA.Services.QuestPDF_HostAddress}}/api/webproject/pdf/generate-and-preview
Content-Type: application/json


{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "aviara_2024.pdf",
    "title": "AVIARA",
    "typology": "HOUSING | HIGHRISE",
    "location": "MALAD | MUMBAI",
    "status": "ONGOING",
    "client": "ANCHOR POINT DEVELOPERS",
    "area": "70000 SQ M",
    "year": "2024",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/01-Aviara-Semi-Aerial-Front-View.jpg",
                       "orderFlag": 1
                  }
    ]
   }

###

### Download WebProject PDF directly
POST {{KPA.Services.QuestPDF_HostAddress}}/api/webproject/pdf/download
Content-Type: application/json

{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "aviara_2024.pdf",
    "title": "AVIARA",
    "typology": "HOUSING | HIGHRISE",
    "location": "MALAD | MUMBAI",
    "status": "ONGOING",
    "client": "ANCHOR POINT DEVELOPERS",
    "area": "70000 SQ M",
    "year": "2024",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/01-Aviara-Semi-Aerial-Front-View.jpg",
                       "orderFlag": 1
                  }
    ]
   }

###
