@KPA.Services.QuestPDF_HostAddress = http://localhost:5246

### Generate WebProject PDF and upload to Azure Blob Storage
POST {{KPA.Services.QuestPDF_HostAddress}}/api/webproject/pdf/generate-and-upload
Content-Type: application/json

{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "aviara_2024.pdf",
    "title": "AVIARA",
    "typology": "HOUSING | HIGHRISE",
    "location": "MALAD | MUMBAI",
    "status": "ONGOING",
    "client": "ANCHOR POINT DEVELOPERS",
    "area": "70000 SQ M",
    "year": "2024",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/01-Aviara-Semi-Aerial-Front-View.jpg",
                       "orderFlag": 1
                  },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/02-Aviara-Front-View.jpg",
                      "orderFlag": 2
                 },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/03-Aviara-Semi-Aerial-Front-Side-Night-View.jpg",
                      "orderFlag": 3
                  },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/04-Aviara-Commercial-Area.jpg",
                      "orderFlag": 4
                  },
        {
                       "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/05-Aviara-Pool-to-Outer.jpg",
                       "orderFlag": 5
                  },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/Aviara-1.jpeg",
                       "orderFlag": 6
                  },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/Aviara-2.jpeg",
                       "orderFlag": 7
                  }
    ]
   }

###

### Generate WebProject PDF and preview in QuestPDF Companion (Single Image Layout)
POST {{KPA.Services.QuestPDF_HostAddress}}/api/webproject/pdf/generate-and-preview
Content-Type: application/json

{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "aviara_2024.pdf",
    "title": "AVIARA",
    "typology": "HOUSING | HIGHRISE",
    "location": "MALAD | MUMBAI",
    "status": "ONGOING",
    "client": "ANCHOR POINT DEVELOPERS",
    "area": "70000 SQ M",
    "year": "2024",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/01-Aviara-Semi-Aerial-Front-View.jpg",
                       "orderFlag": 1
                  },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/02-Aviara-Front-View.jpg",
                      "orderFlag": 2
                 }
    ]
   }

###


### Generate WebProject PDF and preview in QuestPDF Companion (3 Image Layout)
POST {{KPA.Services.QuestPDF_HostAddress}}/api/webproject/pdf/generate-and-preview
Content-Type: application/json

{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "aviara_2024.pdf",
    "title": "AVIARA",
    "typology": "HOUSING | HIGHRISE",
    "location": "MALAD | MUMBAI",
    "status": "ONGOING",
    "client": "ANCHOR POINT DEVELOPERS",
    "area": "70000 SQ M",
    "year": "2024",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/01-Aviara-Semi-Aerial-Front-View.jpg",
                       "orderFlag": 1
                  },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/02-Aviara-Front-View.jpg",
                      "orderFlag": 2
                 },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/03-Aviara-Semi-Aerial-Front-Side-Night-View.jpg",
                      "orderFlag": 3
                  }
    ]
   }

###

### Generate WebProject PDF and preview in QuestPDF Companion (3 Image Layout)
POST {{KPA.Services.QuestPDF_HostAddress}}/api/webproject/pdf/generate-and-preview
Content-Type: application/json

{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "aviara_2024.pdf",
    "title": "AVIARA",
    "typology": "HOUSING | HIGHRISE",
    "location": "MALAD | MUMBAI",
    "status": "ONGOING",
    "client": "ANCHOR POINT DEVELOPERS",
    "area": "70000 SQ M",
    "year": "2024",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/01-Aviara-Semi-Aerial-Front-View.jpg",
                       "orderFlag": 1
                  },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/02-Aviara-Front-View.jpg",
                      "orderFlag": 2
                 },
        {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/03-Aviara-Semi-Aerial-Front-Side-Night-View.jpg",
                      "orderFlag": 3
                  },
                   {
                      "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/04-Aviara-Commercial-Area.jpg",
                      "orderFlag": 4
                  }
    ]
   }

###

### Generate WebAboutUs PDF and preview in QuestPDF Companion
POST {{KPA.Services.QuestPDF_HostAddress}}/api/webaboutus/pdf/generate-and-preview
Content-Type: application/json

{
  "title": "About KPA Architects",
  "subtitle": "Innovative Design Solutions Since 1995",
  "description": "KPA Architects is a leading architectural firm specializing in sustainable design and innovative building solutions. We create spaces that inspire, function efficiently, and respect the environment.",
  "mission": "To create exceptional architectural designs that enhance communities and improve quality of life through sustainable and innovative solutions.",
  "vision": "To be the premier architectural firm recognized for design excellence, environmental stewardship, and client satisfaction.",
  "values": "Innovation, Sustainability, Collaboration, Excellence, and Community Focus guide everything we do.",
  "companyHistory": "Founded in 1995, KPA Architects has grown from a small local firm to an internationally recognized practice with over 500 completed projects.",
  "contactInfo": "123 Design Street, Architecture City, AC 12345 | Phone: (************* | Email: <EMAIL>",
  "slides": [
    { "url": "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=1200", "orderFlag": 1, "caption": "Our modern office headquarters" },
    { "url": "https://images.unsplash.com/photo-*************-37526070297c?w=800", "orderFlag": 2, "caption": "Award-winning residential project" },
    { "url": "https://images.unsplash.com/photo-*************-6870744d04b2?w=800", "orderFlag": 3, "caption": "Sustainable commercial building" }
  ]
}

###

### Generate WebProject PDF and preview (Multiple Images Layout - 5 images)
POST {{KPA.Services.QuestPDF_HostAddress}}/api/webproject/pdf/generate-and-preview
Content-Type: application/json


{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "aviara_2024.pdf",
    "title": "AVIARA",
    "typology": "HOUSING | HIGHRISE",
    "location": "MALAD | MUMBAI",
    "status": "ONGOING",
    "client": "ANCHOR POINT DEVELOPERS",
    "area": "70000 SQ M",
    "year": "2024",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/01-Aviara-Semi-Aerial-Front-View.jpg",
                       "orderFlag": 1
                  }
    ]
   }

###


### Generate WebProject PDF and preview (Multiple Images Layout - 5 images)
POST {{KPA.Services.QuestPDF_HostAddress}}/api/webproject/pdf/generate-and-preview
Content-Type: application/json


{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "aviara_2024.pdf",
    "title": "AVIARA",
    "typology": "HOUSING | HIGHRISE",
    "location": "MALAD | MUMBAI",
    "status": "ONGOING",
    "client": "ANCHOR POINT DEVELOPERS",
    "area": "70000 SQ M",
    "year": "2024",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/CFC/01-Poonam-CFC--Cam-01.jpg",
                       "orderFlag": 1
                  }
    ]
   }

###
### Generate WebProject PDF and preview (Multiple Images Layout - 5 images)
POST {{KPA.Services.QuestPDF_HostAddress}}/api/webproject/pdf/generate-and-preview
Content-Type: application/json


{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "aviara_2024.pdf",
    "title": "AVIARA",
    "typology": "HOUSING | HIGHRISE",
    "location": "MALAD | MUMBAI",
    "status": "ONGOING",
    "client": "ANCHOR POINT DEVELOPERS",
    "area": "70000 SQ M",
    "year": "2024",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/ICONICA/Iconica-Day-View-01.jpg",
                       "orderFlag": 1
                  }
    ]
   }

###
### Download WebProject PDF directly
POST {{KPA.Services.QuestPDF_HostAddress}}/api/webproject/pdf/download
Content-Type: application/json

{
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=kpa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
  "containerName": "attachments",
  "customFileName": "aviara_2024.pdf",
    "title": "AVIARA",
    "typology": "HOUSING | HIGHRISE",
    "location": "MALAD | MUMBAI",
    "status": "ONGOING",
    "client": "ANCHOR POINT DEVELOPERS",
    "area": "70000 SQ M",
    "year": "2024",
    "slides": [
  {
                        "url": "https://kpa.blob.core.windows.net/attachments/Website/WebProject/AVIARA/01-Aviara-Semi-Aerial-Front-View.jpg",
                       "orderFlag": 1
                  }
    ]
   }

###
