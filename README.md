# KPA PDF Generation Service

A .NET 8 Web API service for generating professional PDF documents using QuestPDF with a **standardized, extensible architecture**. The service supports multiple model types, Azure Blob Storage upload, and QuestPDF Companion app preview.

## 🏗️ **Standardized Architecture**

This service implements a **generic, scalable approach** for PDF generation that can handle any model type:

### **Key Features**
- **Generic PDF Generation**: Standardized interfaces for any model type
- **Factory Pattern**: Automatic service resolution based on model type
- **Extensible Design**: Easy to add new model types without changing core logic
- **Type Safety**: Strongly-typed interfaces and generic constraints
- **Azure Blob Storage Integration**: Upload generated PDFs to Azure Blob Storage
- **QuestPDF Companion Preview**: Open PDFs directly in QuestPDF Companion app
- **Professional Layout**: Clean, professional design with proper margins and typography

### **Currently Supported Models**
- **WebProject**: Architecture/construction projects with dynamic image layouts

## PDF Layout

The PDF layout dynamically adapts based on the number of images:

### Single Image Layout
- **Full Width**: Single image spans the entire width below project details
- **Optimal Display**: Maximum space for showcasing the main project image

### Multiple Images Layout
- **Left Column**: Main image (first in the array)
- **Right Column**: Additional images arranged intelligently:
  - **1-2 additional images**: Stacked vertically
  - **3-4 additional images**: Arranged in a 2x2 grid
- **Single Page**: All content fits on one landscape page
- **Professional Spacing**: Proper margins and padding for clean presentation

## 🚀 **API Endpoints**

### **Generic Endpoints (Recommended)**

#### 1. Get Supported Model Types
```
GET /api/pdf/supported-types
```
Returns a list of all supported model types for PDF generation.

#### 2. WebProject PDF Generation

##### Generate and Upload to Azure Blob Storage
```
POST /api/pdf/webproject/generate-and-upload
```

**Request Body:**
```json
{
  "model": {
    "title": "Modern Office Building",
    "description": "Project description...",
    "location": "Downtown Seattle, WA",
    "client": "Tech Corp Inc.",
    "projectDate": "2024-01-15T00:00:00Z",
    "projectType": "Commercial Architecture",
    "typology": "Office Building",
    "area": "50,000 sq ft",
    "year": "2024",
    "status": "Completed",
    "imageUrls": [
      "https://example.com/image1.jpg",
      "https://example.com/image2.jpg"
    ]
  },
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=...",
  "containerName": "project-pdfs",
  "customFileName": "custom_name.pdf"
}
```

**Response:**
```json
{
  "success": true,
  "pdfUrl": "https://storageaccount.blob.core.windows.net/pdfs/project_20240115123456.pdf",
  "fileName": "modern_office_building_20240115_project.pdf",
  "modelType": "WebProject",
  "modelId": "modernofficebuilding_20240115",
  "generatedAt": "2024-01-15T10:30:00Z"
}
```

##### Generate and Preview in QuestPDF Companion
```
POST /api/pdf/webproject/generate-and-preview
```

##### Download PDF Directly
```
POST /api/pdf/webproject/download
```

## Setup and Configuration

### Prerequisites
- .NET 8 SDK
- QuestPDF Companion app (for preview functionality)
- Azure Storage Account (for blob upload functionality)

### Installation
1. Clone the repository
2. Restore NuGet packages:
   ```bash
   dotnet restore
   ```
3. Run the application:
   ```bash
   dotnet run
   ```

### Azure Blob Storage Configuration
To use the upload functionality, you need:
1. An Azure Storage Account
2. A connection string with blob access permissions
3. A container name (will be created automatically if it doesn't exist)

### QuestPDF Companion
For the preview functionality:
1. Download QuestPDF Companion from the official website
2. Install and run the application
3. The preview endpoint will automatically open PDFs in the Companion app

## Usage Examples

See the `KPA.Services.QuestPDF.http` file for complete request examples.

### Basic Project with Images
```json
{
  "title": "Modern Office Building",
  "description": "A state-of-the-art office building...",
  "location": "Downtown Seattle, WA",
  "client": "Tech Corp Inc.",
  "projectDate": "2024-01-15T00:00:00Z",
  "projectType": "Commercial Architecture",
  "imageUrls": [
    "https://images.unsplash.com/photo-*************-c627a92ad1ab?w=800",
    "https://images.unsplash.com/photo-*************-37526070297c?w=600"
  ]
}
```

## Image Requirements

- **Supported Formats**: JPG, JPEG, PNG, GIF, BMP, WebP
- **Recommended Size**: 800px width for main image, 600px for additional images
- **Maximum Images**: 5 (1 main + 4 additional)
- **URLs**: Must be publicly accessible HTTP/HTTPS URLs

## Error Handling

The API provides detailed error responses:
- **400 Bad Request**: Missing required fields or invalid data
- **500 Internal Server Error**: PDF generation or upload failures

## Development

## 🔧 **Adding New Model Types**

The standardized architecture makes it easy to add new model types:

### 1. Create Your Model
```csharp
public class Invoice : IPdfGeneratable
{
    public string InvoiceNumber { get; set; }
    public DateTime InvoiceDate { get; set; }
    // ... other properties

    // IPdfGeneratable implementation
    public string Id => InvoiceNumber;
    public string DisplayName => $"Invoice {InvoiceNumber}";
    public string ModelType => "Invoice";

    public bool IsValidForPdfGeneration() => !string.IsNullOrEmpty(InvoiceNumber);
    public string GetPdfFileName() => $"invoice_{InvoiceNumber}.pdf";
}
```

### 2. Create PDF Generator Service
```csharp
public class InvoicePdfGeneratorService : BasePdfGeneratorService<Invoice>
{
    public override string ModelType => "Invoice";

    protected override IDocument CreateDocument(Invoice model)
    {
        return new InvoicePdfDocument(model);
    }
}
```

### 3. Create QuestPDF Document
```csharp
public class InvoicePdfDocument : IDocument
{
    private readonly Invoice _invoice;

    public InvoicePdfDocument(Invoice invoice) => _invoice = invoice;

    public void Compose(IDocumentContainer container)
    {
        // Your PDF layout logic here
    }
}
```

### 4. Register in DI Container
```csharp
// In Program.cs or Startup.cs
builder.Services.AddPdfGeneratorService<InvoicePdfGeneratorService, Invoice>();
```

### 5. Add to Factory
```csharp
// In PdfGeneratorServiceFactory constructor
_serviceTypes.Add("Invoice", typeof(InvoicePdfGeneratorService));
```

### 6. Add Controller Endpoints
```csharp
[HttpPost("invoice/generate-and-upload")]
public async Task<ActionResult<PdfResponse>> GenerateInvoicePdfAndUpload([FromBody] PdfGenerationRequest<Invoice> request)
{
    return await GenerateAndUpload(request.Model, request.AzureConnectionString, request.ContainerName, request.CustomFileName);
}
```

## 📁 **Project Structure**
```
├── Controllers/
│   └── GenericPdfController.cs       # Generic API endpoints
├── Services/
│   ├── BasePdfGeneratorService.cs    # Base PDF generation logic
│   ├── PdfGeneratorServiceFactory.cs # Service factory
│   └── AzureBlobService.cs           # Azure Blob Storage integration
├── Models/
│   ├── GenericPdfModels.cs           # Generic request/response models
│   └── WebProject.cs                 # WebProject model
├── Interfaces/
│   └── IPdfGeneratable.cs            # Core interfaces
├── WebProjects/
│   └── WebProjectPdfGeneratorService.cs # WebProject-specific service
└── Program.cs                        # Application configuration
```

### Testing
Use the provided HTTP file (`KPA.Services.QuestPDF.http`) with Visual Studio Code REST Client extension or similar tools to test the endpoints.

## License

This project uses QuestPDF Community License. For commercial use, please ensure you have the appropriate QuestPDF license.
