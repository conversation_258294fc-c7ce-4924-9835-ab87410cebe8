# KPA WebProject PDF Generation Service

A simple .NET 8 Web API service for generating professional PDF documents from WebProject data using QuestPDF and **Minimal APIs**.

## ✨ **Simple & Focused**

This service is designed to be **simple, clean, and focused** on WebProject PDF generation:

### **Key Features**
- **WebProject PDF Generation**: Specialized for architecture/construction projects
- **Minimal APIs**: Clean, simple endpoint definitions
- **Azure Blob Storage Integration**: Upload generated PDFs to Azure Blob Storage
- **QuestPDF Companion Preview**: Open PDFs directly in QuestPDF Companion app
- **Dynamic Image Layout**: Adapts layout based on number of images
- **Professional Design**: Clean typography and proper spacing

## PDF Layout

The PDF layout dynamically adapts based on the number of images:

### Single Image Layout
- **Full Width**: Single image spans the entire width below project details
- **Optimal Display**: Maximum space for showcasing the main project image

### Multiple Images Layout
- **Left Column**: Main image (first in the array)
- **Right Column**: Additional images arranged intelligently:
  - **1-2 additional images**: Stacked vertically
  - **3-4 additional images**: Arranged in a 2x2 grid
- **Single Page**: All content fits on one landscape page
- **Professional Spacing**: Proper margins and padding for clean presentation

## 🚀 **API Endpoints (Minimal APIs)**

### 1. Generate and Upload to Azure Blob Storage
```
POST /api/webproject/pdf/generate-and-upload
```

**Request Body:**
```json
{
  "title": "Modern Office Building",
  "description": "Project description...",
  "location": "Downtown Seattle, WA",
  "client": "Tech Corp Inc.",
  "typology": "Office Building",
  "area": "50,000 sq ft",
  "year": "2024",
  "status": "Completed",
  "imageUrls": [
    "https://example.com/image1.jpg",
    "https://example.com/image2.jpg"
  ],
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=...",
  "containerName": "webproject-pdfs",
  "customFileName": "custom_name.pdf"
}
```

**Response:**
```json
{
  "success": true,
  "pdfUrl": "https://storageaccount.blob.core.windows.net/pdfs/project_20240115123456.pdf",
  "fileName": "modern_office_building_2024_project.pdf",
  "generatedAt": "2024-01-15T10:30:00Z"
}
```

### 2. Generate and Preview in QuestPDF Companion
```
POST /api/webproject/pdf/generate-and-preview
```

### 3. Download PDF Directly
```
POST /api/webproject/pdf/download
```

## Setup and Configuration

### Prerequisites
- .NET 8 SDK
- QuestPDF Companion app (for preview functionality)
- Azure Storage Account (for blob upload functionality)

### Installation
1. Clone the repository
2. Restore NuGet packages:
   ```bash
   dotnet restore
   ```
3. Run the application:
   ```bash
   dotnet run
   ```

### Azure Blob Storage Configuration
To use the upload functionality, you need:
1. An Azure Storage Account
2. A connection string with blob access permissions
3. A container name (will be created automatically if it doesn't exist)

### QuestPDF Companion
For the preview functionality:
1. Download QuestPDF Companion from the official website
2. Install and run the application
3. The preview endpoint will automatically open PDFs in the Companion app

## Usage Examples

See the `KPA.Services.QuestPDF.http` file for complete request examples.

### Basic Project with Images
```json
{
  "title": "Modern Office Building",
  "description": "A state-of-the-art office building...",
  "location": "Downtown Seattle, WA",
  "client": "Tech Corp Inc.",
  "projectDate": "2024-01-15T00:00:00Z",
  "projectType": "Commercial Architecture",
  "imageUrls": [
    "https://images.unsplash.com/photo-*************-c627a92ad1ab?w=800",
    "https://images.unsplash.com/photo-*************-37526070297c?w=600"
  ]
}
```

## Image Requirements

- **Supported Formats**: JPG, JPEG, PNG, GIF, BMP, WebP
- **Recommended Size**: 800px width for main image, 600px for additional images
- **Maximum Images**: 5 (1 main + 4 additional)
- **URLs**: Must be publicly accessible HTTP/HTTPS URLs

## Error Handling

The API provides detailed error responses:
- **400 Bad Request**: Missing required fields or invalid data
- **500 Internal Server Error**: PDF generation or upload failures

## Development

## � **Simple Project Structure**
```
├── Services/
│   └── AzureBlobService.cs           # Azure Blob Storage integration
├── WebProjects/
│   ├── WebProjectPdfService.cs       # PDF generation service
│   └── WebProjectPdfRequest.cs       # Request/response models
└── Program.cs                        # Minimal API endpoints & DI setup
```

## 🔧 **Key Components**

### WebProjectPdfRequest
Contains all project data and configuration:
- **Project Info**: Title, Client, Location, Typology, Area, Year, Status, Description
- **Images**: List of image URLs with dynamic layout
- **Azure Config**: Connection string, container name, custom filename

### WebProjectPdfService
Simple service with three main methods:
- `GeneratePdf()` - Creates PDF as byte array
- `ShowInCompanion()` - Opens in QuestPDF Companion
- `GenerateFileName()` - Creates appropriate filename

### Minimal APIs
Clean endpoint definitions in Program.cs:
- `/api/webproject/pdf/generate-and-upload`
- `/api/webproject/pdf/generate-and-preview`
- `/api/webproject/pdf/download`

### Testing
Use the provided HTTP file (`KPA.Services.QuestPDF.http`) with Visual Studio Code REST Client extension or similar tools to test the endpoints.

## License

This project uses QuestPDF Community License. For commercial use, please ensure you have the appropriate QuestPDF license.
