# KPA PDF Generation Service

A .NET 8 Web API service for generating professional PDF documents from WebProject data using QuestPDF. The service supports both Azure Blob Storage upload and QuestPDF Companion app preview.

## Features

- **Landscape PDF Generation**: Creates PDFs in A4 landscape format with two-column layout
- **Azure Blob Storage Integration**: Upload generated PDFs to Azure Blob Storage
- **QuestPDF Companion Preview**: Open PDFs directly in QuestPDF Companion app for development
- **Image Support**: Handles 1-5 project images with automatic layout
- **Professional Layout**: Clean, professional design with proper margins and typography

## PDF Layout

The PDF layout dynamically adapts based on the number of images:

### Single Image Layout
- **Full Width**: Single image spans the entire width below project details
- **Optimal Display**: Maximum space for showcasing the main project image

### Multiple Images Layout
- **Left Column**: Main image (first in the array)
- **Right Column**: Additional images arranged intelligently:
  - **1-2 additional images**: Stacked vertically
  - **3-4 additional images**: Arranged in a 2x2 grid
- **Single Page**: All content fits on one landscape page
- **Professional Spacing**: Proper margins and padding for clean presentation

## API Endpoints

### 1. Generate and Upload to Azure Blob Storage
```
POST /api/pdf/generate-and-upload
```

Generates a PDF and uploads it to Azure Blob Storage, returning the public URL.

**Request Body:**
```json
{
  "project": {
    "title": "Project Title",
    "description": "Project description...",
    "location": "Project Location",
    "client": "Client Name",
    "projectDate": "2024-01-15T00:00:00Z",
    "projectType": "Project Type",
    "imageUrls": [
      "https://example.com/image1.jpg",
      "https://example.com/image2.jpg"
    ]
  },
  "azureConnectionString": "DefaultEndpointsProtocol=https;AccountName=...",
  "containerName": "pdfs"
}
```

**Response:**
```json
{
  "success": true,
  "pdfUrl": "https://storageaccount.blob.core.windows.net/pdfs/project_20240115123456.pdf",
  "fileName": "Project_Title_project.pdf"
}
```

### 2. Generate and Preview in QuestPDF Companion
```
POST /api/pdf/generate-and-preview
```

Generates a PDF and opens it in the QuestPDF Companion app for development/testing.

**Request Body:**
```json
{
  "title": "Project Title",
  "description": "Project description...",
  "location": "Project Location",
  "client": "Client Name",
  "projectDate": "2024-01-15T00:00:00Z",
  "projectType": "Project Type",
  "imageUrls": [
    "https://example.com/image1.jpg",
    "https://example.com/image2.jpg"
  ]
}
```

### 3. Download PDF Directly
```
POST /api/pdf/download
```

Generates and returns the PDF file directly for download.

## Setup and Configuration

### Prerequisites
- .NET 8 SDK
- QuestPDF Companion app (for preview functionality)
- Azure Storage Account (for blob upload functionality)

### Installation
1. Clone the repository
2. Restore NuGet packages:
   ```bash
   dotnet restore
   ```
3. Run the application:
   ```bash
   dotnet run
   ```

### Azure Blob Storage Configuration
To use the upload functionality, you need:
1. An Azure Storage Account
2. A connection string with blob access permissions
3. A container name (will be created automatically if it doesn't exist)

### QuestPDF Companion
For the preview functionality:
1. Download QuestPDF Companion from the official website
2. Install and run the application
3. The preview endpoint will automatically open PDFs in the Companion app

## Usage Examples

See the `KPA.Services.QuestPDF.http` file for complete request examples.

### Basic Project with Images
```json
{
  "title": "Modern Office Building",
  "description": "A state-of-the-art office building...",
  "location": "Downtown Seattle, WA",
  "client": "Tech Corp Inc.",
  "projectDate": "2024-01-15T00:00:00Z",
  "projectType": "Commercial Architecture",
  "imageUrls": [
    "https://images.unsplash.com/photo-*************-c627a92ad1ab?w=800",
    "https://images.unsplash.com/photo-*************-37526070297c?w=600"
  ]
}
```

## Image Requirements

- **Supported Formats**: JPG, JPEG, PNG, GIF, BMP, WebP
- **Recommended Size**: 800px width for main image, 600px for additional images
- **Maximum Images**: 5 (1 main + 4 additional)
- **URLs**: Must be publicly accessible HTTP/HTTPS URLs

## Error Handling

The API provides detailed error responses:
- **400 Bad Request**: Missing required fields or invalid data
- **500 Internal Server Error**: PDF generation or upload failures

## Development

### Project Structure
```
├── Controllers/
│   └── PdfController.cs          # API endpoints
├── Services/
│   ├── PdfGeneratorService.cs    # PDF generation logic
│   └── AzureBlobService.cs       # Azure Blob Storage integration
├── Models/
│   └── PdfGenerationRequest.cs   # Data models
└── Program.cs                    # Application configuration
```

### Testing
Use the provided HTTP file (`KPA.Services.QuestPDF.http`) with Visual Studio Code REST Client extension or similar tools to test the endpoints.

## License

This project uses QuestPDF Community License. For commercial use, please ensure you have the appropriate QuestPDF license.
