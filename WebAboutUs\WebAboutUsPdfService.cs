using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Companion;

namespace KPA.Services.QuestPDF.WebAboutUs;

/// <summary>
/// Simple WebAboutUs PDF generator service
/// </summary>
public class WebAboutUsPdfService
{
    private readonly HttpClient _httpClient;

    static WebAboutUsPdfService()
    {
        global::QuestPDF.Settings.License = LicenseType.Community;
    }

    public WebAboutUsPdfService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    /// <summary>
    /// Generate PDF as byte array
    /// </summary>
    public byte[] GeneratePdf(WebAboutUsPdfRequest request)
    {
        var document = new WebAboutUsPdfDocument(request, _httpClient);
        return document.GeneratePdf();
    }

    /// <summary>
    /// Show PDF in QuestPDF Companion app
    /// </summary>
    public void ShowInCompanion(WebAboutUsPdfRequest request)
    {
        var document = new WebAboutUsPdfDocument(request, _httpClient);
        document.ShowInCompanion();
    }

    /// <summary>
    /// Generate filename for the PDF
    /// </summary>
    public string GenerateFileName(WebAboutUsPdfRequest request)
    {
        if (!string.IsNullOrEmpty(request.CustomFileName))
            return request.CustomFileName;

        var sanitizedTitle = SanitizeFileName(request.Title);
        var year = !string.IsNullOrEmpty(request.Year) ? request.Year : DateTime.Now.Year.ToString();
        return $"{sanitizedTitle}_{year}_project.pdf";
    }

    private static string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
        return string.IsNullOrEmpty(sanitized) ? "WebAboutUs" : sanitized.Replace(" ", "_").ToLowerInvariant();
    }
}
