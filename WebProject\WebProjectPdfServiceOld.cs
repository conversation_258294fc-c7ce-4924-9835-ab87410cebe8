using QuestPDF.Fluent;
using QuestPDF.Infrastructure;
using QuestPDF.Companion;

namespace KPA.Services.QuestPDF.WebProject;

/// <summary>
/// Simple WebProject PDF generator service
/// </summary>
public class WebProjectPdfServiceOld
{
    private readonly HttpClient _httpClient;

    static WebProjectPdfServiceOld()
    {
        global::QuestPDF.Settings.License = LicenseType.Community;
    }

    public WebProjectPdfServiceOld(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    /// <summary>
    /// Generate PDF as byte array
    /// </summary>
    public byte[] GeneratePdf(WebProjectPdfRequest request)
    {
        var document = new WebProjectPdfDocumentOld(request, _httpClient);
        return document.GeneratePdf();
    }

    /// <summary>
    /// Show PDF in QuestPDF Companion app
    /// </summary>
    public void ShowInCompanion(WebProjectPdfRequest request)
    {
        var document = new WebProjectPdfDocumentOld(request, _httpClient);
        document.ShowInCompanion();
    }

    /// <summary>
    /// Generate filename for the PDF
    /// </summary>
    public string GenerateFileName(WebProjectPdfRequest request)
    {
        if (!string.IsNullOrEmpty(request.CustomFileName))
            return request.CustomFileName;

        var sanitizedTitle = SanitizeFileName(request.Title);
        var year = !string.IsNullOrEmpty(request.Year) ? request.Year : DateTime.Now.Year.ToString();
        return $"{sanitizedTitle}_{year}_project.pdf";
    }

    private static string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
        return string.IsNullOrEmpty(sanitized) ? "webproject" : sanitized.Replace(" ", "_").ToLowerInvariant();
    }
}
