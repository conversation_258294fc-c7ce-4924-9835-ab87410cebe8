using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using KPA.Services.QuestPDF.Shared;

namespace KPA.Services.QuestPDF.WebAboutUs;

/// <summary>
/// WebAboutUs PDF document using the shared base class
/// </summary>
public class WebAboutUsPdfDocument : BasePdfDocument<WebAboutUsPdfRequest>
{
    private readonly string BrandColor = Environment.GetEnvironmentVariable("QuestPDF:BrandColor"); // Different brand color for About Us

    public WebAboutUsPdfDocument(WebAboutUsPdfRequest request, HttpClient httpClient)
        : base(request, httpClient)
    {
    }

    protected override IEnumerable<string> GetImageUrls()
    {
        return _request.Slides.Select(s => s.Url).Where(url => !string.IsNullOrEmpty(url));
    }

    public override void Compose(IDocumentContainer container)
    {
        container
            .Page(page =>
            {
                page.Size(PageSizes.A3.Landscape());
                page.Margin(20, Unit.Millimetre);
                page.DefaultTextStyle(x => x.FontSize(12).FontFamily(Fonts.Calibri).FontColor(Colors.Black));

                page.Content()
                .Column(mainColumn =>
                {
                    //3 vertical columns
                    mainColumn.Spacing(15);
                    mainColumn.Item().Element(ComposeAboutUs);
                    mainColumn.Item().Element(ComposeOurApproach);
                    mainColumn.Item().Element(ComposeOurServices);
                });
            });
    }   
    
    private void ComposeAboutUs(IContainer container)
    {
        container
        .BorderBottom(1).BorderColor(Colors.Grey.Lighten1)
        .Row(row =>
        {
            row.Spacing(15);
            row.RelativeItem(1).Column(column =>
            {
                // Title
                column.Item()
                .Padding(10)
                .Text("About Us")
                    .FontSize(18)
                    .Bold()
                    .FontColor(BrandColor);
            });
            row.RelativeItem(2).Column(column =>
            {
                // Description
                column.Item()
                .Padding(10)
                .Text(_request.AboutUs)
                    .FontSize(12)
                    .FontColor(Colors.Black);
            });
        });

    }

    private void ComposeOurApproach(IContainer container)
    {
        container.Row(row =>
        {
            row.RelativeItem(1).Column(column =>
            {
                // Title
                column.Item()
                .Text("Our Approach")
                    .FontSize(18)
                    .Bold()
                    .FontColor(BrandColor);
            });
            row.RelativeItem(1).PaddingLeft(15).Column(column =>
            {
                // Description
                column.Item()
                .Text(_request.OurApproach)
                    .FontSize(12)
                    .FontColor(Colors.Black);
            });
        });
    }

    private void ComposeOurServices(IContainer container)
    {
        container
        .Row(row =>
        {
            row.RelativeItem(1).Column(column =>
            {
                // Title
                column.Item()
                .Text("Our Services")
                    .FontSize(18)
                    .Bold()
                    .FontColor(BrandColor);
            });
            row.RelativeItem(1).PaddingLeft(15).Column(column =>
            {
                // Description
                column.Item()
                .Text("Architecture")
                    .FontSize(12)
                    .FontColor(Colors.Black);
                column.Item()
                .Text("Interior Design")
                    .FontSize(12)
                    .FontColor(Colors.Black);
                column.Item()
                .Text("Urban Design")
                    .FontSize(12)
                    .FontColor(Colors.Black);
            });
        });
    }
    

}
