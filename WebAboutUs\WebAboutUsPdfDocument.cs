using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using KPA.Services.QuestPDF.Shared;

namespace KPA.Services.QuestPDF.WebAboutUs;

/// <summary>
/// WebAboutUs PDF document using the shared base class
/// </summary>
public class WebAboutUsPdfDocument : BasePdfDocument<WebAboutUsPdfRequest>
{
    private readonly string BrandColor = "#2E86AB"; // Different brand color for About Us

    public WebAboutUsPdfDocument(WebAboutUsPdfRequest request, HttpClient httpClient) 
        : base(request, httpClient)
    {
    }

    protected override IEnumerable<string> GetImageUrls()
    {
        return _request.Slides.Select(s => s.Url).Where(url => !string.IsNullOrEmpty(url));
    }

    public override void Compose(IDocumentContainer container)
    {
        container
            .Page(page =>
            {
                page.Size(PageSizes.A3.Landscape());
                page.Margin(20, Unit.Millimetre);
                page.DefaultTextStyle(x => x.FontSize(12).FontFamily(Fonts.Calibri).FontColor(Colors.Black));

                page.Content()
                .Column(mainColumn =>
                {
                  
                });
            });
    }

}
