using Microsoft.AspNetCore.Mvc;
using KPA.Services.QuestPDF.Services;

namespace KPA.Services.QuestPDF.Controllers;

[ApiController]
[Route("api/[controller]")]
public class PdfController : ControllerBase
{
    private readonly PdfGeneratorService _pdfGeneratorService;
    private readonly IAzureBlobService _azureBlobService;
    private readonly ILogger<PdfController> _logger;

    public PdfController(
        PdfGeneratorService pdfGeneratorService,
        IAzureBlobService azureBlobService,
        ILogger<PdfController> logger)
    {
        _pdfGeneratorService = pdfGeneratorService;
        _azureBlobService = azureBlobService;
        _logger = logger;
    }

    /// <summary>
    /// Generates a PDF for the project and uploads it to Azure Blob Storage
    /// </summary>
    /// <param name="request">Project data and Azure configuration</param>
    /// <returns>Response with the uploaded PDF URL</returns>
    [HttpPost("generate-and-upload")]
    public async Task<ActionResult<PdfGenerationResponse>> GenerateAndUploadPdf([FromBody] PdfGenerationRequest request)
    {
        try
        {
            _logger.LogInformation("Starting PDF generation and upload for project: {ProjectTitle}", request.Project.Title);

            // Validate request
            if (string.IsNullOrEmpty(request.AzureConnectionString))
            {
                return BadRequest(new PdfGenerationResponse
                {
                    Success = false,
                    ErrorMessage = "Azure connection string is required"
                });
            }

            if (string.IsNullOrEmpty(request.Project.Title))
            {
                return BadRequest(new PdfGenerationResponse
                {
                    Success = false,
                    ErrorMessage = "Project title is required"
                });
            }

            // Generate PDF
            var pdfBytes = _pdfGeneratorService.GeneratePdf(request.Project);
            _logger.LogInformation("PDF generated successfully, size: {Size} bytes", pdfBytes.Length);

            // Generate filename
            var fileName = $"{SanitizeFileName(request.Project.Title)}_project.pdf";

            // Upload to Azure Blob Storage
            var pdfUrl = await _azureBlobService.UploadPdfAsync(
                pdfBytes, 
                fileName, 
                request.ContainerName, 
                request.AzureConnectionString);

            _logger.LogInformation("PDF uploaded successfully to: {PdfUrl}", pdfUrl);

            return Ok(new PdfGenerationResponse
            {
                Success = true,
                PdfUrl = pdfUrl,
                FileName = fileName
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating or uploading PDF for project: {ProjectTitle}", request.Project.Title);
            
            return StatusCode(500, new PdfGenerationResponse
            {
                Success = false,
                ErrorMessage = $"An error occurred: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// Generates a PDF for the project and opens it in QuestPDF Companion app
    /// </summary>
    /// <param name="project">Project data</param>
    /// <returns>Response indicating success or failure</returns>
    [HttpPost("generate-and-preview")]
    public ActionResult<PdfGenerationResponse> GenerateAndPreview([FromBody] WebProject project)
    {
        try
        {
            _logger.LogInformation("Starting PDF generation and preview for project: {ProjectTitle}", project.Title);

            // Validate request
            if (string.IsNullOrEmpty(project.Title))
            {
                return BadRequest(new PdfGenerationResponse
                {
                    Success = false,
                    ErrorMessage = "Project title is required"
                });
            }

            // Generate and show in companion app
            _pdfGeneratorService.ShowInCompanion(project);
            _logger.LogInformation("PDF preview opened successfully for project: {ProjectTitle}", project.Title);

            return Ok(new PdfGenerationResponse
            {
                Success = true,
                FileName = $"{SanitizeFileName(project.Title)}_project.pdf"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating PDF preview for project: {ProjectTitle}", project.Title);
            
            return StatusCode(500, new PdfGenerationResponse
            {
                Success = false,
                ErrorMessage = $"An error occurred: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// Downloads the generated PDF directly
    /// </summary>
    /// <param name="project">Project data</param>
    /// <returns>PDF file</returns>
    [HttpPost("download")]
    public ActionResult DownloadPdf([FromBody] WebProject project)
    {
        try
        {
            _logger.LogInformation("Starting PDF download for project: {ProjectTitle}", project.Title);

            // Validate request
            if (string.IsNullOrEmpty(project.Title))
            {
                return BadRequest("Project title is required");
            }

            // Generate PDF
            var pdfBytes = _pdfGeneratorService.GeneratePdf(project);
            var fileName = $"{SanitizeFileName(project.Title)}_project.pdf";

            _logger.LogInformation("PDF generated for download, size: {Size} bytes", pdfBytes.Length);

            return File(pdfBytes, "application/pdf", fileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating PDF for download: {ProjectTitle}", project.Title);
            return StatusCode(500, $"An error occurred: {ex.Message}");
        }
    }

    private static string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
        return string.IsNullOrEmpty(sanitized) ? "project" : sanitized.Replace(" ", "_");
    }
}
