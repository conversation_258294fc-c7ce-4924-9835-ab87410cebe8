using KPA.Services.QuestPDF;
using KPA.Services.QuestPDF.Services;
using KPA.Services.QuestPDF.WebAboutUs;
using KPA.Services.QuestPDF.WebProject;
using KPA.Services.QuestPDF.Shared;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "KPA WebProject PDF Generation API", Version = "v1" });
});

// Register services
builder.Services.AddScoped<IAzureBlobService, AzureBlobService>();

builder.Services.AddScoped<WebProjectPdfService>();
builder.Services.AddScoped<WebAboutUsPdfService>();


builder.Services.AddHttpClient();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

// Minimal API endpoints for WebProject PDF generation
app.MapPost("/api/webproject/pdf/generate-and-upload", async (
    WebProjectPdfRequest request,
    WebProjectPdfService pdfService,
    IAzureBlobService azureBlobService,
    ILogger<Program> logger) =>
{
    try
    {
        logger.LogInformation("Starting PDF generation and upload for project: {Title}", request.Title);

        // Validate request
        if (string.IsNullOrEmpty(request.Title))
        {
            return Results.BadRequest(new PdfResponse
            {
                Success = false,
                ErrorMessage = "Project title is required"
            });
        }

        if (string.IsNullOrEmpty(request.AzureConnectionString))
        {
            return Results.BadRequest(new PdfResponse
            {
                Success = false,
                ErrorMessage = "Azure connection string is required"
            });
        }

        // Generate PDF
        var pdfBytes = pdfService.GeneratePdf(request);
        logger.LogInformation("PDF generated successfully, size: {Size} bytes", pdfBytes.Length);

        // Generate filename
        var fileName = pdfService.GenerateFileName(request);

        // Upload to Azure Blob Storage
        var pdfUrl = await azureBlobService.UploadPdfAsync(
            pdfBytes,
            fileName,
            request.ContainerName,
            request.AzureConnectionString);

        logger.LogInformation("PDF uploaded successfully to: {PdfUrl}", pdfUrl);

        return Results.Ok(new PdfResponse
        {
            Success = true,
            PdfUrl = pdfUrl,
            FileName = fileName
        });
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error generating or uploading PDF for project: {Title}", request.Title);

        return Results.Problem(new PdfResponse
        {
            Success = false,
            ErrorMessage = $"An error occurred: {ex.Message}"
        }.ErrorMessage);
    }
})
.WithName("GenerateAndUploadWebProjectPdf")
.WithSummary("Generate WebProject PDF and upload to Azure Blob Storage")
.WithOpenApi();

app.MapPost("/api/webproject/pdf/generate-and-preview", (
    WebProjectPdfRequest request,
    WebProjectPdfService pdfService,
    ILogger<Program> logger) =>
{
    try
    {
        logger.LogInformation("Starting PDF generation and preview for project: {Title}", request.Title);

        // Validate request
        if (string.IsNullOrEmpty(request.Title))
        {
            return Results.BadRequest(new PdfResponse
            {
                Success = false,
                ErrorMessage = "Project title is required"
            });
        }

        // Generate and show in companion app
        pdfService.ShowInCompanion(request);
        logger.LogInformation("PDF preview opened successfully for project: {Title}", request.Title);

        return Results.Ok(new PdfResponse
        {
            Success = true,
            FileName = pdfService.GenerateFileName(request)
        });
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error generating PDF preview for project: {Title}", request.Title);

        return Results.Problem(new PdfResponse
        {
            Success = false,
            ErrorMessage = $"An error occurred: {ex.Message}"
        }.ErrorMessage);
    }
})
.WithName("GenerateAndPreviewWebProjectPdf")
.WithSummary("Generate WebProject PDF and open in QuestPDF Companion")
.WithOpenApi();

app.MapPost("/api/webproject/pdf/download", (
    WebProjectPdfRequest request,
    WebProjectPdfService pdfService,
    ILogger<Program> logger) =>
{
    try
    {
        logger.LogInformation("Starting PDF download for project: {Title}", request.Title);

        // Validate request
        if (string.IsNullOrEmpty(request.Title))
        {
            return Results.BadRequest("Project title is required");
        }

        // Generate PDF
        var pdfBytes = pdfService.GeneratePdf(request);
        var fileName = pdfService.GenerateFileName(request);

        logger.LogInformation("PDF generated for download, size: {Size} bytes", pdfBytes.Length);

        return Results.File(pdfBytes, "application/pdf", fileName);
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error generating PDF for download: {Title}", request.Title);
        return Results.Problem($"An error occurred: {ex.Message}");
    }
})
.WithName("DownloadWebProjectPdf")
.WithSummary("Download WebProject PDF directly")
.WithOpenApi();

// New endpoints using shared architecture
app.MapPost("/api/webproject/pdf/generate-and-preview-new", (
    WebProjectPdfRequest request,
    WebProjectPdfServiceNew pdfService,
    ILogger<Program> logger) =>
{
    try
    {
        logger.LogInformation("Starting PDF generation and preview (NEW) for project: {Title}", request.Title);

        // Validate request
        if (string.IsNullOrEmpty(request.Title))
        {
            return Results.BadRequest(new PdfResponse
            {
                Success = false,
                ErrorMessage = "Project title is required"
            });
        }

        // Generate and show in companion app
        pdfService.ShowInCompanion(request);
        logger.LogInformation("PDF preview opened successfully (NEW) for project: {Title}", request.Title);

        return Results.Ok(new PdfResponse
        {
            Success = true,
            FileName = pdfService.GenerateFileName(request)
        });
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error generating PDF preview (NEW) for project: {Title}", request.Title);

        return Results.Problem(new PdfResponse
        {
            Success = false,
            ErrorMessage = $"An error occurred: {ex.Message}"
        }.ErrorMessage);
    }
})
.WithName("GenerateAndPreviewWebProjectPdfNew")
.WithSummary("Generate WebProject PDF using new shared architecture and open in QuestPDF Companion")
.WithOpenApi();

app.MapPost("/api/webaboutus/pdf/generate-and-preview", (
    WebAboutUsPdfRequest request,
    WebAboutUsPdfServiceNew pdfService,
    ILogger<Program> logger) =>
{
    try
    {
        logger.LogInformation("Starting WebAboutUs PDF generation and preview for: {Title}", request.Title);

        // Validate request
        if (string.IsNullOrEmpty(request.Title))
        {
            return Results.BadRequest(new PdfResponse
            {
                Success = false,
                ErrorMessage = "Title is required"
            });
        }

        // Generate and show in companion app
        pdfService.ShowInCompanion(request);
        logger.LogInformation("WebAboutUs PDF preview opened successfully for: {Title}", request.Title);

        return Results.Ok(new PdfResponse
        {
            Success = true,
            FileName = pdfService.GenerateFileName(request)
        });
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error generating WebAboutUs PDF preview for: {Title}", request.Title);

        return Results.Problem(new PdfResponse
        {
            Success = false,
            ErrorMessage = $"An error occurred: {ex.Message}"
        }.ErrorMessage);
    }
})
.WithName("GenerateAndPreviewWebAboutUsPdf")
.WithSummary("Generate WebAboutUs PDF using shared architecture and open in QuestPDF Companion")
.WithOpenApi();

app.Run();
