using KPA.Services.QuestPDF.Services;
using KPA.Services.QuestPDF.WebProjects;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "KPA WebProject PDF Generation API", Version = "v1" });
});

// Register services
builder.Services.AddScoped<WebProjectPdfService>();
builder.Services.AddScoped<IAzureBlobService, AzureBlobService>();
builder.Services.AddHttpClient();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

// Minimal API endpoints for WebProject PDF generation
app.MapPost("/api/webproject/pdf/generate-and-upload", async (
    WebProjectPdfRequest request,
    WebProjectPdfService pdfService,
    IAzureBlobService azureBlobService,
    ILogger<Program> logger) =>
{
    try
    {
        logger.LogInformation("Starting PDF generation and upload for project: {Title}", request.Title);

        // Validate request
        if (string.IsNullOrEmpty(request.Title))
        {
            return Results.BadRequest(new WebProjectPdfResponse
            {
                Success = false,
                ErrorMessage = "Project title is required"
            });
        }

        if (string.IsNullOrEmpty(request.AzureConnectionString))
        {
            return Results.BadRequest(new WebProjectPdfResponse
            {
                Success = false,
                ErrorMessage = "Azure connection string is required"
            });
        }

        // Generate PDF
        var pdfBytes = pdfService.GeneratePdf(request);
        logger.LogInformation("PDF generated successfully, size: {Size} bytes", pdfBytes.Length);

        // Generate filename
        var fileName = pdfService.GenerateFileName(request);

        // Upload to Azure Blob Storage
        var pdfUrl = await azureBlobService.UploadPdfAsync(
            pdfBytes,
            fileName,
            request.ContainerName,
            request.AzureConnectionString);

        logger.LogInformation("PDF uploaded successfully to: {PdfUrl}", pdfUrl);

        return Results.Ok(new WebProjectPdfResponse
        {
            Success = true,
            PdfUrl = pdfUrl,
            FileName = fileName
        });
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error generating or uploading PDF for project: {Title}", request.Title);

        return Results.Problem(new WebProjectPdfResponse
        {
            Success = false,
            ErrorMessage = $"An error occurred: {ex.Message}"
        }.ErrorMessage);
    }
})
.WithName("GenerateAndUploadWebProjectPdf")
.WithSummary("Generate WebProject PDF and upload to Azure Blob Storage")
.WithOpenApi();

app.MapPost("/api/webproject/pdf/generate-and-preview", (
    WebProjectPdfRequest request,
    WebProjectPdfService pdfService,
    ILogger<Program> logger) =>
{
    try
    {
        logger.LogInformation("Starting PDF generation and preview for project: {Title}", request.Title);

        // Validate request
        if (string.IsNullOrEmpty(request.Title))
        {
            return Results.BadRequest(new WebProjectPdfResponse
            {
                Success = false,
                ErrorMessage = "Project title is required"
            });
        }

        // Generate and show in companion app
        pdfService.ShowInCompanion(request);
        logger.LogInformation("PDF preview opened successfully for project: {Title}", request.Title);

        return Results.Ok(new WebProjectPdfResponse
        {
            Success = true,
            FileName = pdfService.GenerateFileName(request)
        });
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error generating PDF preview for project: {Title}", request.Title);

        return Results.Problem(new WebProjectPdfResponse
        {
            Success = false,
            ErrorMessage = $"An error occurred: {ex.Message}"
        }.ErrorMessage);
    }
})
.WithName("GenerateAndPreviewWebProjectPdf")
.WithSummary("Generate WebProject PDF and open in QuestPDF Companion")
.WithOpenApi();

app.MapPost("/api/webproject/pdf/download", (
    WebProjectPdfRequest request,
    WebProjectPdfService pdfService,
    ILogger<Program> logger) =>
{
    try
    {
        logger.LogInformation("Starting PDF download for project: {Title}", request.Title);

        // Validate request
        if (string.IsNullOrEmpty(request.Title))
        {
            return Results.BadRequest("Project title is required");
        }

        // Generate PDF
        var pdfBytes = pdfService.GeneratePdf(request);
        var fileName = pdfService.GenerateFileName(request);

        logger.LogInformation("PDF generated for download, size: {Size} bytes", pdfBytes.Length);

        return Results.File(pdfBytes, "application/pdf", fileName);
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error generating PDF for download: {Title}", request.Title);
        return Results.Problem($"An error occurred: {ex.Message}");
    }
})
.WithName("DownloadWebProjectPdf")
.WithSummary("Download WebProject PDF directly")
.WithOpenApi();

app.Run();
