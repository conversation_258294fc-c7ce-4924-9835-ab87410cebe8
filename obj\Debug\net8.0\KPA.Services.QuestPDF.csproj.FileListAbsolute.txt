D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\KPA.Services.QuestPDF.csproj.AssemblyReference.cache
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\KPA.Services.QuestPDF.GeneratedMSBuildEditorConfig.editorconfig
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\KPA.Services.QuestPDF.AssemblyInfoInputs.cache
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\KPA.Services.QuestPDF.AssemblyInfo.cs
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\KPA.Services.QuestPDF.csproj.CoreCompileInputs.cache
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\KPA.Services.QuestPDF.MvcApplicationPartsAssemblyInfo.cs
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\KPA.Services.QuestPDF.MvcApplicationPartsAssemblyInfo.cache
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\appsettings.Development.json
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\appsettings.json
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\KPA.Services.QuestPDF.staticwebassets.endpoints.json
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-Black.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-BlackItalic.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-Bold.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-BoldItalic.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-ExtraBold.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-ExtraBoldItalic.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-ExtraLight.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-ExtraLightItalic.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-Italic.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-Light.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-LightItalic.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-Medium.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-MediumItalic.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-Regular.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-SemiBold.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-SemiBoldItalic.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-Thin.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\Lato-ThinItalic.ttf
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\LatoFont\OFL.txt
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\KPA.Services.QuestPDF.exe
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\KPA.Services.QuestPDF.deps.json
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\KPA.Services.QuestPDF.runtimeconfig.json
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\KPA.Services.QuestPDF.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\KPA.Services.QuestPDF.pdb
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\Azure.Core.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\Azure.Storage.Blobs.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\Azure.Storage.Common.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\Microsoft.AspNetCore.OpenApi.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\Microsoft.OpenApi.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\QuestPDF.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\System.ClientModel.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\System.IO.Hashing.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\System.Memory.Data.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\linux-arm64\native\libQuestPdfSkia.so
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\linux-arm64\native\libcrypto.so
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\linux-arm64\native\libjpeg.so.8
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\linux-arm64\native\libqpdf.so
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\linux-musl-x64\native\libQuestPdfSkia.so
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\linux-musl-x64\native\libcrypto.so
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\linux-musl-x64\native\libjpeg.so.8
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\linux-musl-x64\native\libqpdf.so
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\linux-x64\native\libQuestPdfSkia.so
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\linux-x64\native\libcrypto.so
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\linux-x64\native\libjpeg.so.8
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\linux-x64\native\libqpdf.so
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\osx-arm64\native\libQuestPdfSkia.dylib
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\osx-arm64\native\libcrypto.dylib
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\osx-arm64\native\libjpeg.dylib
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\osx-arm64\native\libqpdf.dylib
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\osx-x64\native\libQuestPdfSkia.dylib
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\osx-x64\native\libcrypto.dylib
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\osx-x64\native\libjpeg.dylib
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\osx-x64\native\libqpdf.dylib
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\win-x64\native\QuestPdfSkia.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\win-x64\native\libgcc_s_seh-1.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\win-x64\native\libstdc++-6.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\win-x64\native\libwinpthread-1.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\win-x64\native\qpdf.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\win-x86\native\QuestPdfSkia.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\win-x86\native\libgcc_s_dw2-1.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\win-x86\native\libstdc++-6.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\win-x86\native\libwinpthread-1.dll
D:\GIT\KPA.Services.QuestPDF\bin\Debug\net8.0\runtimes\win-x86\native\qpdf.dll
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\scopedcss\bundle\KPA.Services.QuestPDF.styles.css
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\staticwebassets.build.json
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\staticwebassets.development.json
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\staticwebassets.build.endpoints.json
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\staticwebassets\msbuild.KPA.Services.QuestPDF.Microsoft.AspNetCore.StaticWebAssets.props
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\staticwebassets\msbuild.KPA.Services.QuestPDF.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\staticwebassets\msbuild.build.KPA.Services.QuestPDF.props
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.KPA.Services.QuestPDF.props
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.KPA.Services.QuestPDF.props
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\staticwebassets.pack.json
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\KPA.Serv.7F41FDC1.Up2Date
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\KPA.Services.QuestPDF.dll
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\refint\KPA.Services.QuestPDF.dll
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\KPA.Services.QuestPDF.pdb
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\KPA.Services.QuestPDF.genruntimeconfig.cache
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\ref\KPA.Services.QuestPDF.dll
D:\GIT\KPA.Services.QuestPDF\obj\Debug\net8.0\KPA.Services.QuestPDF.sourcelink.json
