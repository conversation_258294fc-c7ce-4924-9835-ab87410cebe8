using KPA.Services.QuestPDF.Shared;

namespace KPA.Services.QuestPDF.WebAboutUs;

/// <summary>
/// WebAboutUs PDF generator service using the shared base class
/// </summary>
public class WebAboutUsPdfServiceNew : BasePdfService<WebAboutUsPdfRequest, WebAboutUsPdfDocument>
{
    public WebAboutUsPdfServiceNew(HttpClient httpClient) : base(httpClient)
    {
    }

    protected override WebAboutUsPdfDocument CreateDocument(WebAboutUsPdfRequest request)
    {
        return new WebAboutUsPdfDocument(request, _httpClient);
    }

    protected override string GetTitleFromRequest(WebAboutUsPdfRequest request)
    {
        return request.Title;
    }

    protected override string GetFileNameSuffix()
    {
        return "aboutus";
    }

    protected override string GetDefaultFileName()
    {
        return "aboutus";
    }
}
