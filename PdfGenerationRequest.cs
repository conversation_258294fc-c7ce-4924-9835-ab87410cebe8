namespace KPA.Services.QuestPDF;

public class WebProject
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Client { get; set; } = string.Empty;
    public DateTime ProjectDate { get; set; }
    public string ProjectType { get; set; } = string.Empty;
    public List<string> ImageUrls { get; set; } = new();
}

public class PdfGenerationRequest
{
    public WebProject Project { get; set; } = new();
    public string? AzureConnectionString { get; set; }
    public string ContainerName { get; set; } = "pdfs";
}

public class PdfGenerationResponse
{
    public bool Success { get; set; }
    public string? PdfUrl { get; set; }
    public string? ErrorMessage { get; set; }
    public string? FileName { get; set; }
}