using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using KPA.Services.QuestPDF.Shared;

namespace KPA.Services.QuestPDF.WebProject;

/// <summary>
/// WebProject PDF document using the shared base class
/// </summary>
public class WebProjectPdfDocumentNew : BasePdfDocument<WebProjectPdfRequest>
{
    private readonly string BrandColor = "#f47920";

    public WebProjectPdfDocumentNew(WebProjectPdfRequest request, HttpClient httpClient) 
        : base(request, httpClient)
    {
    }

    protected override IEnumerable<string> GetImageUrls()
    {
        return _request.Slides.Select(s => s.Url).Where(url => !string.IsNullOrEmpty(url));
    }

    public override void Compose(IDocumentContainer container)
    {
        container
            .Page(page =>
            {
                page.Size(PageSizes.A3.Landscape());
                page.Margin(20, Unit.Millimetre);
                page.DefaultTextStyle(x => x.FontSize(12).FontFamily(Fonts.Calibri).FontColor(Colors.Black));

                page.Content()
                .Column(mainColumn =>
                {
                    var validImages = _request.Slides.Select(s => s.Url).ToList();

                    if (validImages.Count == 1)
                    {
                        mainColumn.Item().Element(ComposeSingleImagePage);
                    }
                    else
                    {
                        mainColumn.Item().Element(ComposeMultiImagePage);
                    }
                });
            });
    }

    private void ComposeSingleImagePage(IContainer container)
    {
        try
        {
            var validImages = _request.Slides.OrderBy(s => s.OrderFlag).Select(s => s.Url).ToList();

            if (validImages.Count > 0)
            {
                var imageUrl = validImages.First();
                var imageOrientation = GetImageOrientation(imageUrl);

                if (imageOrientation == ImageOrientation.Portrait)
                {
                    // Portrait image: project details on top, image on bottom
                    container.Column(column =>
                    {
                        column.Item().Element(ComposeProjectDetails);
                        column.Item().PaddingTop(15).Element(container => ComposeImage(container, imageUrl));
                    });
                }
                else
                {
                    // Landscape/Square image: project details on left, image on right
                    container.Row(row =>
                    {
                        row.RelativeItem(1).Element(ComposeProjectDetails);
                        row.RelativeItem(2).PaddingLeft(15).Element(container => ComposeImage(container, imageUrl));
                    });
                }
            }
            else
            {
                // No images available, just show project details
                container.Element(ComposeProjectDetails);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in ComposeSingleImagePage: {ex.Message}");
            // Fallback to default horizontal layout
            container.Row(row =>
            {
                row.RelativeItem(1).Element(ComposeProjectDetails);
                row.RelativeItem(2).PaddingLeft(15).Element(container => 
                    ComposeImageFallback(container, "Layout error"));
            });
        }
    }

    private void ComposeMultiImagePage(IContainer container)
    {
        container.Row(row =>
        {
            // Left column: Project details + main image
            row.RelativeItem(1).Column(column =>
            {
                column.Item().Element(ComposeProjectDetails);
                column.Item().PaddingTop(15).Element(ComposeMainImage);
            });
            
            // Right column: Additional images
            row.RelativeItem(1).PaddingLeft(15).Element(ComposeAdditionalImages);
        });
    }

    private void ComposeProjectDetails(IContainer container)
    {
        container.Column(column =>
        {
            // Title
            column.Item()
                .Text(_request.Title)
                .FontSize(18)
                .Bold()
                .FontColor(BrandColor);

            // Details table
            column.Item()
                .PaddingTop(5)
                .Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.ConstantColumn(70);
                        columns.RelativeColumn();
                    });

                    // Client
                    if (!string.IsNullOrEmpty(_request.Client))
                    {
                        table.Cell().Text("CLIENT").SemiBold().FontSize(12).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_request.Client).FontSize(14);
                    }

                    // Location
                    if (!string.IsNullOrEmpty(_request.Location))
                    {
                        table.Cell().Text("LOCATION").SemiBold().FontSize(10).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_request.Location);
                    }

                    // Typology
                    if (!string.IsNullOrEmpty(_request.Typology))
                    {
                        table.Cell().Text("TYPOLOGY").SemiBold().FontSize(10).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_request.Typology);
                    }

                    // Area
                    if (!string.IsNullOrEmpty(_request.Area))
                    {
                        table.Cell().Text("AREA").SemiBold().FontSize(10).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_request.Area);
                    }

                    // Year
                    if (!string.IsNullOrEmpty(_request.Year))
                    {
                        table.Cell().Text("YEAR").SemiBold().FontSize(10).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_request.Year);
                    }

                    // Status
                    if (!string.IsNullOrEmpty(_request.Status))
                    {
                        table.Cell().Text("STATUS").SemiBold().FontSize(10).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_request.Status);
                    }
                });
        });
    }

    private void ComposeMainImage(IContainer container)
    {
        var validImages = _request.Slides.OrderBy(s => s.OrderFlag).Select(s => s.Url).ToList();
        
        if (validImages.Count > 0)
        {
            var imageUrl = validImages.First();
            ComposeImage(container, imageUrl, "Main image not available");
        }
        else
        {
            ComposeImageFallback(container, "No images available");
        }
    }

    private void ComposeAdditionalImages(IContainer container)
    {
        try
        {
            var validImages = _request.Slides.OrderBy(s => s.OrderFlag).Select(s => s.Url).ToList();

            if (validImages.Count > 1)
            {
                var additionalImages = validImages.Skip(1).ToList();

                if (additionalImages.Count == 1)
                {
                    // Single additional image: full width
                    ComposeImage(container, additionalImages.First());
                }
                else if (additionalImages.Count == 2)
                {
                    // Two additional images: stack vertically
                    container.Column(column =>
                    {
                        column.Spacing(15);
                        foreach (var imageUrl in additionalImages)
                        {
                            column.Item().Element(c => ComposeImage(c, imageUrl));
                        }
                    });
                }
                else if (additionalImages.Count >= 3)
                {
                    // 3+ images: 2-column grid
                    container.Column(column =>
                    {
                        column.Spacing(15);
                        for (int i = 0; i < additionalImages.Count; i += 2)
                        {
                            column.Item().Row(row =>
                            {
                                row.Spacing(15);
                                
                                // First image in row
                                if (i < additionalImages.Count)
                                {
                                    row.RelativeItem().Element(c => ComposeImage(c, additionalImages[i]));
                                }
                                
                                // Second image in row (if exists)
                                if (i + 1 < additionalImages.Count)
                                {
                                    row.RelativeItem().Element(c => ComposeImage(c, additionalImages[i + 1]));
                                }
                                else
                                {
                                    row.RelativeItem(); // Empty space
                                }
                            });
                        }
                    });
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading additional images: {ex.Message}");
            ComposeImageFallback(container, "Additional images failed to load");
        }
    }
}
