using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using KPA.Services.QuestPDF.Shared;

namespace KPA.Services.QuestPDF.WebProject;

/// <summary>
/// QuestPDF document for WebProject
/// </summary>
public class WebProjectPdfDocumentOld : IDocument
{
    private readonly WebProjectPdfRequest _project;
    private readonly HttpClient _httpClient;
    private readonly Dictionary<string, byte[]> _imageCache = new();
    private readonly Dictionary<string, ImageOrientation> _orientationCache = new();

    private readonly string BrandColor = "#f47920";

    public WebProjectPdfDocumentOld(WebProjectPdfRequest project, HttpClient httpClient)
    {
        _project = project;
        _httpClient = httpClient;

        // Pre-download all images at initialization
        PreloadImages();
    }

    public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

    // public DocumentSettings GetSettings() => new DocumentSettings
    // {
    //     ImageCompressionQuality = A3PaperConfig.DefaultCompression,
    //     ImageRasterDpi = A3PaperConfig.DefaultDPI, // Optimized for A3 paper size
    //     PdfA = false
    // };

     public DocumentSettings GetSettings() => DocumentSettings.Default;

    private void PreloadImages()
    {
        try
        {
            var imageUrls = _project.Slides.Select(s => s.Url).Where(url => !string.IsNullOrEmpty(url)).ToList();

            foreach (var imageUrl in imageUrls)
            {
                try
                {
                    var imageBytes = DownloadImageAsync(imageUrl).GetAwaiter().GetResult();
                    if (imageBytes != null)
                    {
                        // Optimize image for A3 paper size
                        var optimizedBytes = OptimizeImageForA3(imageBytes);
                        _imageCache[imageUrl] = optimizedBytes;

                        // Also cache the orientation
                        var dimensions = GetImageDimensions(imageBytes);
                        Console.WriteLine($"Image dimensions for {imageUrl}: {dimensions.Width}x{dimensions.Height}");
                        if (dimensions.Width > 0 && dimensions.Height > 0)
                        {
                            if (dimensions.Width > dimensions.Height)
                            {
                                _orientationCache[imageUrl] = ImageOrientation.Landscape;
                                Console.WriteLine($"Image orientation for {imageUrl}: Landscape");
                            }
                            else if (dimensions.Height > dimensions.Width)
                            {
                                _orientationCache[imageUrl] = ImageOrientation.Portrait;
                                Console.WriteLine($"Image orientation for {imageUrl}: Portrait");
                            }
                            else
                            {
                                _orientationCache[imageUrl] = ImageOrientation.Square;
                                Console.WriteLine($"Image orientation for {imageUrl}: Square");
                            }
                        }
                        else
                        {
                            _orientationCache[imageUrl] = ImageOrientation.Landscape;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to preload image {imageUrl}: {ex.Message}");
                    // Continue with other images
                }
            }

            Console.WriteLine($"Preloaded {_imageCache.Count} images successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in PreloadImages: {ex.Message}");
        }
    }

    public void Compose(IDocumentContainer container)
    {
        container
            .Page(page =>
            {
                page.Size(PageSizes.A3.Landscape());
                page.Margin(20, Unit.Millimetre);
                page.DefaultTextStyle(x => x.FontSize(12).FontFamily(Fonts.Calibri).FontColor(Colors.Black));

                page.Content()
                // .Border(1).BorderColor(Colors.Orange.Accent1)
                .Column(mainColumn =>
                {
                    var validImages = _project.Slides.Select(s => s.Url).ToList();

                    if (validImages.Count == 1)
                    {
                        mainColumn.Item().Element(ComposeSingleImagePage);
                    }
                    else
                    {
                        mainColumn.Item().Element(ComposeMultiImagePage);
                    }

                });
            });
    }

    private void ComposeSingleImagePage(IContainer container)
    {
        try
        {
            var validImages = _project.Slides.OrderBy(s => s.OrderFlag).Select(s => s.Url).ToList();

            if (validImages.Count > 0)
            {
                var imageUrl = validImages.First();
                var imageOrientation = GetImageOrientation(imageUrl);

                if (imageOrientation == ImageOrientation.Landscape)
                {
                    // Vertical image: project details on top, image on bottom
                    container.Column(column =>
                    {
                        column.Item().Element(ComposeProjectDetails);
                        column.Item().PaddingTop(15).Element(ComposeMainImage);
                    });
                }
                else
                {
                    // Horizontal/Square image: project details on left, image on right
                    container.Row(row =>
                    {
                        row.RelativeItem(1).Element(ComposeProjectDetails);
                        row.RelativeItem(2).PaddingLeft(15)
                        .Element(ComposeMainImage);
                    });
                }
            }
            else
            {
                // No images available, just show project details
                container.Element(ComposeProjectDetails);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in ComposeSingleImagePage: {ex.Message}");
            // Fallback to default horizontal layout
            container.Row(row =>
            {
                row.RelativeItem(1).Element(ComposeProjectDetails);
                row.RelativeItem(2).PaddingLeft(15).Element(ComposeMainImage);
            });
        }
    }

    private void ComposeMultiImagePage(IContainer container)
    {

        container.Row(row =>
        {
            row.RelativeItem()
            .Element(ComposeMultiImageColumn1);
            
            row.RelativeItem().PaddingLeft(15)
            .Element(ComposeMultiImageColumn2);
        });
    }

    private void ComposeMultiImageColumn1(IContainer container)
    {
        container
        .Column(column =>
            {
                column.Item().Element(ComposeProjectDetails);
                column.Item().PaddingTop(15).Element(ComposeMainImage);
            });
    }

    private void ComposeMultiImageColumn2(IContainer container)
    {
        container
        .Column(column =>
            {
                column.Item()
                .Element(ComposeAdditionalImages);
            });
    }

    private void ComposeProjectDetails(IContainer container)
    {
        container
        .Column(column =>
            {
                // Title
                column.Item()
                .Text(_project.Title)
                    .FontSize(18)
                    .Bold()
                    .FontColor(BrandColor);

                // Details table
                column.Item()
                .PaddingTop(5)
                .Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.ConstantColumn(70);
                        columns.RelativeColumn();
                    });

                    // Client
                    if (!string.IsNullOrEmpty(_project.Client))
                    {
                        table.Cell().Text("CLIENT").SemiBold().FontSize(12).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_project.Client).FontSize(14);
                    }

                    // Location
                    if (!string.IsNullOrEmpty(_project.Location))
                    {
                        table.Cell().Text("LOCATION").SemiBold().FontSize(10).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_project.Location);
                    }

                    // Typology
                    if (!string.IsNullOrEmpty(_project.Typology))
                    {
                        table.Cell().Text("TYPOLOGY").SemiBold().FontSize(10).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_project.Typology);
                    }

                    // Area
                    if (!string.IsNullOrEmpty(_project.Area))
                    {
                        table.Cell().Text("AREA").SemiBold().FontSize(10).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_project.Area);
                    }

                    // Year
                    if (!string.IsNullOrEmpty(_project.Year))
                    {
                        table.Cell().Text("YEAR").SemiBold().FontSize(10).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_project.Year);
                    }

                    // Status
                    if (!string.IsNullOrEmpty(_project.Status))
                    {
                        table.Cell().Text("STATUS").SemiBold().FontSize(10).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_project.Status);
                    }
                });
            });
    }

    private void ComposeMainImage(IContainer container)
    {
        try
        {
            var validImages = _project.Slides.OrderBy(s => s.OrderFlag).Select(s => s.Url).ToList();

            if (validImages.Count > 0)
            {
                var imageUrl = validImages.First();

                // Use cached image bytes
                if (_imageCache.TryGetValue(imageUrl, out var imageBytes))
                {
                    container
                        .AlignBottom()
                        .AlignRight()
                        .Image(imageBytes)
                        .FitArea(); // This will use all available width and height
                }
                else
                {
                    ComposeImageFallback(container, "Image not found in cache");
                }
            }
            else
            {
                // Fallback when no images are available
                ComposeImageFallback(container, "No image available");
            }
        }
        catch (Exception ex)
        {
            // Log the error for debugging
            Console.WriteLine($"Error loading main image: {ex.Message}");
            ComposeImageFallback(container, "Image failed to load");
        }
    }

    private void ComposeAdditionalImages(IContainer container)
    {
        try
        {
            var validImages = _project.Slides.OrderBy(s => s.OrderFlag).Select(s => s.Url).ToList();

            if (validImages.Count > 1)
            {
                var additionalImages = validImages.Skip(1).ToList();

                if (additionalImages.Count == 1)
                {
                    Console.WriteLine("Single additional image");
                    // Single additional image: full width
                    if (_imageCache.TryGetValue(additionalImages.First(), out var imageBytes))
                    {
                        container
                            .Image(imageBytes)
                            .FitArea(); // This will use all available width and height
                    }
                }
                else if (additionalImages.Count == 2)
                {
                    Console.WriteLine("Two additional images");
                    // Two additional images: stack vertically
                    container
                        .Column(column =>
                        {
                            foreach (var imageUrl in additionalImages)
                            {
                                if (_imageCache.TryGetValue(imageUrl, out var imageBytes))
                                {
                                    column.Item()
                                        .PaddingBottom(15)
                                        .Image(imageBytes)
                                        .FitArea(); // This will use all available width and height
                                }
                            }
                        });
                }
                else if (additionalImages.Count >= 3)
                {
                    Console.WriteLine("Three or more additional images");
                    // 2 columns for 3 or more images
                    container
                        .Column(column =>
                        {
                            column.Spacing(15);
                            for (int i = 0; i < additionalImages.Count; i += 2)
                            {
                                column.Item()
                                .Row(row =>
                                {
                                    row.Spacing(15);
                                    for (int j = 0; j < 2; j++)
                                    {
                                        if (i + j < additionalImages.Count)
                                        {
                                            var imageUrl = additionalImages[i + j];
                                            if (_imageCache.TryGetValue(imageUrl, out var imageBytes))
                                            {
                                                if (j == 0)
                                                {
                                                    row.RelativeItem()
                                                        // .PaddingBottom(15)
                                                        .Image(imageBytes)
                                                        .FitArea(); // This will use all available width and height
                                                }
                                                else
                                                {
                                                    row.RelativeItem()
                                                        // .PaddingBottom(15)
                                                        // .PaddingLeft(15)
                                                        .Image(imageBytes)
                                                        .FitArea(); // This will use all available width and height
                                                }
                                            }
                                        }
                                    }
                                });
                            }
                        }); 
                }
            }
        }
        catch (Exception ex)
        {
            // Log the error for debugging
            Console.WriteLine($"Error loading additional images: {ex.Message}");
        }
    }

    private async Task<byte[]?> DownloadImageAsync(string imageUrl)
    {
        try
        {
            if (string.IsNullOrEmpty(imageUrl))
                return null;

            // Check if it's a web URL
            if (imageUrl.StartsWith("http://") || imageUrl.StartsWith("https://"))
            {
                var response = await _httpClient.GetAsync(imageUrl);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadAsByteArrayAsync();
                }
                else
                {
                    Console.WriteLine($"Failed to download image: {response.StatusCode} - {imageUrl}");
                    return null;
                }
            }
            else
            {
                // Local file path
                if (File.Exists(imageUrl))
                {
                    return await File.ReadAllBytesAsync(imageUrl);
                }
                else
                {
                    Console.WriteLine($"Local file not found: {imageUrl}");
                    return null;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error downloading image {imageUrl}: {ex.Message}");
            return null;
        }
    }

    private static void ComposeImageFallback(IContainer container, string message)
    {
        container
            .Padding(10)
            .Background(Colors.Grey.Lighten3)
            .AlignCenter()
            .AlignMiddle()
            .Text(message)
            .FontSize(16)
            .FontColor(Colors.Grey.Darken1);
    }

    private enum ImageOrientation
    {
        Portrait,
        Landscape,
        Square
    }

    private ImageOrientation GetImageOrientation(string imageUrl)
    {
        // Use cached orientation if available
        if (_orientationCache.TryGetValue(imageUrl, out var cachedOrientation))
        {
            return cachedOrientation;
        }

        // Fallback to landscape if not in cache
        Console.WriteLine($"Image orientation not found in cache for: {imageUrl}");
        return ImageOrientation.Landscape;
    }

    private (int Width, int Height) GetImageDimensions(byte[] imageBytes)
    {
        try
        {
            // Simple JPEG dimension detection
            if (imageBytes.Length > 10 && imageBytes[0] == 0xFF && imageBytes[1] == 0xD8)
            {
                return GetJpegDimensions(imageBytes);
            }

            // Simple PNG dimension detection
            if (imageBytes.Length > 24 &&
                imageBytes[0] == 0x89 && imageBytes[1] == 0x50 &&
                imageBytes[2] == 0x4E && imageBytes[3] == 0x47)
            {
                return GetPngDimensions(imageBytes);
            }

            // For other formats or if detection fails, assume landscape
            return (1920, 1080); // Default landscape dimensions
        }
        catch
        {
            return (1920, 1080); // Default landscape dimensions
        }
    }

    private (int Width, int Height) GetJpegDimensions(byte[] imageBytes)
    {
        try
        {
            for (int i = 2; i < imageBytes.Length - 8; i++)
            {
                if (imageBytes[i] == 0xFF && (imageBytes[i + 1] == 0xC0 || imageBytes[i + 1] == 0xC2))
                {
                    int height = (imageBytes[i + 5] << 8) | imageBytes[i + 6];
                    int width = (imageBytes[i + 7] << 8) | imageBytes[i + 8];
                    return (width, height);
                }
            }
        }
        catch { }

        return (1920, 1080); // Default if parsing fails
    }

    private (int Width, int Height) GetPngDimensions(byte[] imageBytes)
    {
        try
        {
            if (imageBytes.Length >= 24)
            {
                int width = (imageBytes[16] << 24) | (imageBytes[17] << 16) | (imageBytes[18] << 8) | imageBytes[19];
                int height = (imageBytes[20] << 24) | (imageBytes[21] << 16) | (imageBytes[22] << 8) | imageBytes[23];
                return (width, height);
            }
        }
        catch { }

        return (1920, 1080); // Default if parsing fails
    }

    private byte[] OptimizeImageForA3(byte[] imageBytes)
    {
        try
        {
            var dimensions = GetImageDimensions(imageBytes);

            // Use configuration for A3 optimization
            var maxWidth = A3PaperConfig.WIDTH_300DPI;   // High quality reference
            var maxHeight = A3PaperConfig.HEIGHT_300DPI;

            // Check if image is larger than optimal A3 size
            if (dimensions.Width > maxWidth || dimensions.Height > maxHeight)
            {
                Console.WriteLine($"Image {dimensions.Width}x{dimensions.Height} is larger than optimal A3 size, but keeping original for quality");
                // For now, keep original size but log the information
                // resize image using imagesharp

            }
            else
            {
                Console.WriteLine($"Image {dimensions.Width}x{dimensions.Height} is optimal for A3 paper size");
            }

            // Return original bytes (compression is handled by QuestPDF settings)
            return imageBytes;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error optimizing image for A3: {ex.Message}");
            return imageBytes; // Return original if optimization fails
        }
    }

}
