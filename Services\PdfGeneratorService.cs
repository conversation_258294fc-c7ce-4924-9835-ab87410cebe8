using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using QuestPDF.Companion;

namespace KPA.Services.QuestPDF.Services;

public class PdfGeneratorService
{
    static PdfGeneratorService()
    {
        global::QuestPDF.Settings.License = LicenseType.Community;
    }

    public byte[] GeneratePdf(WebProject project)
    {
        var document = new ProjectPdfDocument(project);
        return document.GeneratePdf();
    }

    public void ShowInCompanion(WebProject project)
    {
        var document = new ProjectPdfDocument(project);
        document.ShowInCompanion();
    }
}

public class ProjectPdfDocument : IDocument
{
    private readonly WebProject _project;

    public ProjectPdfDocument(WebProject project)
    {
        _project = project;
    }

    public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

    public DocumentSettings GetSettings() => DocumentSettings.Default;

    public void Compose(IDocumentContainer container)
    {
        container
            .Page(page =>
            {
                page.Size(PageSizes.A4.Landscape());
                page.Margin(20, Unit.Millimetre);
                page.DefaultTextStyle(x => x.FontSize(10).FontFamily(Fonts.Arial));

                page.Content().Row(row =>
                {
                    // Left column - Details and main image
                    row.RelativeItem(1).Column(leftColumn =>
                    {
                        // Project details section
                        leftColumn.Item().Element(ComposeProjectDetails);
                        
                        // Main image (first image)
                        if (_project.ImageUrls.Any())
                        {
                            leftColumn.Item().PaddingTop(10).Element(container => ComposeMainImage(container, _project.ImageUrls.First()));
                        }
                    });

                    // Right column - Additional images
                    row.RelativeItem(1).PaddingLeft(10).Column(rightColumn =>
                    {
                        if (_project.ImageUrls.Count > 1)
                        {
                            rightColumn.Item().Element(container => ComposeAdditionalImages(container, _project.ImageUrls.Skip(1).ToList()));
                        }
                    });
                });
            });
    }

    private void ComposeProjectDetails(IContainer container)
    {
        container.Column(column =>
        {
            // Title
            column.Item().Text(_project.Title)
                .FontSize(16)
                .Bold()
                .FontColor(Colors.Black);

            column.Item().PaddingTop(8).Text("Project Details")
                .FontSize(12)
                .SemiBold()
                .FontColor(Colors.Grey.Darken2);

            // Details table
            column.Item().PaddingTop(5).Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.ConstantColumn(80);
                    columns.RelativeColumn();
                });

                // Client
                if (!string.IsNullOrEmpty(_project.Client))
                {
                    table.Cell().Text("Client:").SemiBold();
                    table.Cell().Text(_project.Client);
                }

                // Location
                if (!string.IsNullOrEmpty(_project.Location))
                {
                    table.Cell().Text("Location:").SemiBold();
                    table.Cell().Text(_project.Location);
                }

                // Project Type
                if (!string.IsNullOrEmpty(_project.ProjectType))
                {
                    table.Cell().Text("Type:").SemiBold();
                    table.Cell().Text(_project.ProjectType);
                }

                // Date
                if (_project.ProjectDate != default)
                {
                    table.Cell().Text("Date:").SemiBold();
                    table.Cell().Text(_project.ProjectDate.ToString("MMMM yyyy"));
                }
            });

            // Description
            if (!string.IsNullOrEmpty(_project.Description))
            {
                column.Item().PaddingTop(10).Text("Description")
                    .FontSize(12)
                    .SemiBold()
                    .FontColor(Colors.Grey.Darken2);

                column.Item().PaddingTop(5).Text(_project.Description)
                    .FontSize(10)
                    .LineHeight(1.2f);
            }
        });
    }

    private void ComposeMainImage(IContainer container, string imageUrl)
    {
        try
        {
            if (IsValidImageUrl(imageUrl))
            {
                container.Column(column =>
                {
                    column.Item().Text("Main Image")
                        .FontSize(12)
                        .SemiBold()
                        .FontColor(Colors.Grey.Darken2);

                    column.Item().PaddingTop(5).Height(200).Image(imageUrl).FitArea();
                });
            }
        }
        catch
        {
            container.Height(200).Background(Colors.Grey.Lighten3)
                .AlignCenter().AlignMiddle()
                .Text("Image not available").FontSize(10).FontColor(Colors.Grey.Darken1);
        }
    }

    private void ComposeAdditionalImages(IContainer container, List<string> imageUrls)
    {
        container.Column(column =>
        {
            column.Item().Text("Additional Images")
                .FontSize(12)
                .SemiBold()
                .FontColor(Colors.Grey.Darken2);

            var validImages = imageUrls.Where(IsValidImageUrl).Take(4).ToList();
            
            for (int i = 0; i < validImages.Count; i++)
            {
                var imageUrl = validImages[i];
                try
                {
                    column.Item().PaddingTop(i == 0 ? 5 : 8).Height(120).Image(imageUrl).FitArea();
                }
                catch
                {
                    column.Item().PaddingTop(i == 0 ? 5 : 8).Height(120).Background(Colors.Grey.Lighten3)
                        .AlignCenter().AlignMiddle()
                        .Text("Image not available").FontSize(9).FontColor(Colors.Grey.Darken1);
                }
            }
        });
    }

    private static bool IsValidImageUrl(string url)
    {
        if (string.IsNullOrEmpty(url)) return false;
        
        try
        {
            var uri = new Uri(url);
            var extension = Path.GetExtension(uri.LocalPath).ToLowerInvariant();
            return new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" }.Contains(extension);
        }
        catch
        {
            return false;
        }
    }
}
