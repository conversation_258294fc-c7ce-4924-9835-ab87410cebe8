using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using KPA.Services.QuestPDF.Shared;

namespace KPA.Services.QuestPDF.WebProject;

/// <summary>
/// WebProject PDF document using the shared base class
/// </summary>
public class WebProjectPdfDocument : BasePdfDocument<WebProjectPdfRequest>
{
    private readonly string BrandColor = "#f47920";

    public WebProjectPdfDocument(WebProjectPdfRequest request, HttpClient httpClient) 
        : base(request, httpClient)
    {
    }

    protected override IEnumerable<string> GetImageUrls()
    {
        return _request.Slides.Select(s => s.Url).Where(url => !string.IsNullOrEmpty(url));
    }

       public override void Compose(IDocumentContainer container)
    {
        container
            .Page(page =>
            {
                page.Size(PageSizes.A3.Landscape());
                page.Margin(20, Unit.Millimetre);
                page.DefaultTextStyle(x => x.FontSize(12).FontFamily(Fonts.Calibri).FontColor(Colors.Black));

                page.Content()
                // .Border(1).BorderColor(Colors.Orange.Accent1)
                .Column(mainColumn =>
                {
                    var validImages = _request.Slides.Select(s => s.Url).ToList();

                    if (validImages.Count == 1)
                    {
                        mainColumn.Item().Element(ComposeSingleImagePage);
                    }
                    else
                    {
                        mainColumn.Item().Element(ComposeMultiImagePage);
                    }

                });
            });
    }

    private void ComposeSingleImagePage(IContainer container)
    {
        try
        {
            var validImages = _request.Slides.OrderBy(s => s.OrderFlag).Select(s => s.Url).ToList();

            if (validImages.Count > 0)
            {
                var imageUrl = validImages.First();
                var imageOrientation = GetImageOrientation(imageUrl);

                if (imageOrientation == ImageOrientation.Landscape)
                {
                    // Vertical image: project details on top, image on bottom
                    container.Column(column =>
                    {
                        column.Item().Element(ComposeProjectDetails);
                        column.Item().PaddingTop(15).Element(ComposeMainImage);
                    });
                }
                else
                {
                    // Horizontal/Square image: project details on left, image on right
                    container.Row(row =>
                    {
                        row.RelativeItem(1).Element(ComposeProjectDetails);
                        row.RelativeItem(2).PaddingLeft(15)
                        .Element(ComposeMainImage);
                    });
                }
            }
            else
            {
                // No images available, just show project details
                container.Element(ComposeProjectDetails);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in ComposeSingleImagePage: {ex.Message}");
            // Fallback to default horizontal layout
            container.Row(row =>
            {
                row.RelativeItem(1).Element(ComposeProjectDetails);
                row.RelativeItem(2).PaddingLeft(15).Element(ComposeMainImage);
            });
        }
    }

    private void ComposeMultiImagePage(IContainer container)
    {

        container.Row(row =>
        {
            row.RelativeItem()
            .Element(ComposeMultiImageColumn1);
            
            row.RelativeItem().PaddingLeft(15)
            .Element(ComposeMultiImageColumn2);
        });
    }

    private void ComposeMultiImageColumn1(IContainer container)
    {
        container
        .Column(column =>
            {
                column.Item().Element(ComposeProjectDetails);
                column.Item().PaddingTop(15).Element(ComposeMainImage);
            });
    }

    private void ComposeMultiImageColumn2(IContainer container)
    {
        container
        .Column(column =>
            {
                column.Item()
                .Element(ComposeAdditionalImages);
            });
    }

    private void ComposeProjectDetails(IContainer container)
    {
        container
        .Column(column =>
            {
                // Title
                column.Item()
                .Text(_request.Title)
                    .FontSize(18)
                    .Bold()
                    .FontColor(BrandColor);

                // Details table
                column.Item()
                .PaddingTop(5)
                .Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.ConstantColumn(70);
                        columns.RelativeColumn();
                    });

                    // Client
                    if (!string.IsNullOrEmpty(_request.Client))
                    {
                        table.Cell().Text("CLIENT").SemiBold().FontSize(12).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_request.Client).FontSize(14);
                    }

                    // Location
                    if (!string.IsNullOrEmpty(_request.Location))
                    {
                        table.Cell().Text("LOCATION").SemiBold().FontSize(10).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_request.Location);
                    }

                    // Typology
                    if (!string.IsNullOrEmpty(_request.Typology))
                    {
                        table.Cell().Text("TYPOLOGY").SemiBold().FontSize(10).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_request.Typology);
                    }

                    // Area
                    if (!string.IsNullOrEmpty(_request.Area))
                    {
                        table.Cell().Text("AREA").SemiBold().FontSize(10).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_request.Area);
                    }

                    // Year
                    if (!string.IsNullOrEmpty(_request.Year))
                    {
                        table.Cell().Text("YEAR").SemiBold().FontSize(10).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_request.Year);
                    }

                    // Status
                    if (!string.IsNullOrEmpty(_request.Status))
                    {
                        table.Cell().Text("STATUS").SemiBold().FontSize(10).FontColor(Colors.Grey.Darken1);
                        table.Cell().Text(_request.Status);
                    }
                });
            });
    }

    private void ComposeMainImage(IContainer container)
    {
        try
        {
            var validImages = _request.Slides.OrderBy(s => s.OrderFlag).Select(s => s.Url).ToList();

            if (validImages.Count > 0)
            {
                var imageUrl = validImages.First();

                // Use cached image bytes
                if (_imageCache.TryGetValue(imageUrl, out var imageBytes))
                {
                    container
                        .AlignBottom()
                        .AlignRight()
                        .Image(imageBytes)
                        .FitArea(); // This will use all available width and height
                }
                else
                {
                    ComposeImageFallback(container, "Image not found in cache");
                }
            }
            else
            {
                // Fallback when no images are available
                ComposeImageFallback(container, "No image available");
            }
        }
        catch (Exception ex)
        {
            // Log the error for debugging
            Console.WriteLine($"Error loading main image: {ex.Message}");
            ComposeImageFallback(container, "Image failed to load");
        }
    }

    private void ComposeAdditionalImages(IContainer container)
    {
        try
        {
            var validImages = _request.Slides.OrderBy(s => s.OrderFlag).Select(s => s.Url).ToList();

            if (validImages.Count > 1)
            {
                var additionalImages = validImages.Skip(1).ToList();

                if (additionalImages.Count == 1)
                {
                    // Console.WriteLine("Single additional image");
                    // Single additional image: full width
                    if (_imageCache.TryGetValue(additionalImages.First(), out var imageBytes))
                    {
                        container
                            .Image(imageBytes)
                            .FitArea(); // This will use all available width and height
                    }
                }
                else if (additionalImages.Count == 2)
                {
                    // Console.WriteLine("Two additional images");
                    // Two additional images: stack vertically
                    container
                        .Column(column =>
                        {
                            foreach (var imageUrl in additionalImages)
                            {
                                if (_imageCache.TryGetValue(imageUrl, out var imageBytes))
                                {
                                    column.Item()
                                        .PaddingBottom(15)
                                        .Image(imageBytes)
                                        .FitArea(); // This will use all available width and height
                                }
                            }
                        });
                }
                else if (additionalImages.Count >= 3)
                {
                    // Console.WriteLine("Three or more additional images");
                    // 2 columns for 3 or more images
                    container
                        .Column(column =>
                        {
                            column.Spacing(15);
                            for (int i = 0; i < additionalImages.Count; i += 2)
                            {
                                column.Item()
                                .Row(row =>
                                {
                                    row.Spacing(15);
                                    for (int j = 0; j < 2; j++)
                                    {
                                        if (i + j < additionalImages.Count)
                                        {
                                            var imageUrl = additionalImages[i + j];
                                            if (_imageCache.TryGetValue(imageUrl, out var imageBytes))
                                            {
                                                if (j == 0)
                                                {
                                                    row.RelativeItem()
                                                        // .PaddingBottom(15)
                                                        .Image(imageBytes)
                                                        .FitArea(); // This will use all available width and height
                                                }
                                                else
                                                {
                                                    row.RelativeItem()
                                                        // .PaddingBottom(15)
                                                        // .PaddingLeft(15)
                                                        .Image(imageBytes)
                                                        .FitArea(); // This will use all available width and height
                                                }
                                            }
                                        }
                                    }
                                });
                            }
                        }); 
                }
            }
        }
        catch (Exception ex)
        {
            // Log the error for debugging
            Console.WriteLine($"Error loading additional images: {ex.Message}");
        }
    }
}
