
namespace KPA.Services.QuestPDF.WebProjects;

/// <summary>
/// WebProject PDF generation request containing project data and configuration
/// </summary>
public class WebProjectPdfRequest
{
    // Project Information
    public string Title { get; set; } = string.Empty;
    public string Client { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Typology { get; set; } = string.Empty;
    public string Area { get; set; } = string.Empty;
    public string Year { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<WebProjectSlide> Slides { get; set; } = new();

    // Azure Configuration (optional)
    public string? AzureConnectionString { get; set; }
    public string ContainerName { get; set; } = string.Empty;
    public string? CustomFileName { get; set; }
}

public class WebProjectSlide
{
    public string Url { get; set; } = string.Empty;
    public int OrderFlag { get; set; }
}

/// <summary>
/// PDF generation response
/// </summary>
public class WebProjectPdfResponse
{
    public bool Success { get; set; }
    public string? PdfUrl { get; set; }
    public string? ErrorMessage { get; set; }
    public string? FileName { get; set; }
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}
