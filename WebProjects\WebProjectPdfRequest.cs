
namespace KPA.Services.QuestPDF.WebProjects;
/// <summary>
/// WebProject PDF generation request containing project data and configuration
/// </summary>
public class WebProjectPdfRequest : PdfRequest
{
    // Project Information
    public string Title { get; set; } = string.Empty;
    public string Client { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Typology { get; set; } = string.Empty;
    public string Area { get; set; } = string.Empty;
    public string Year { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<WebProjectSlide> Slides { get; set; } = new();
}

public class WebProjectSlide
{
    public string Url { get; set; } = string.Empty;
    public int OrderFlag { get; set; }
}
