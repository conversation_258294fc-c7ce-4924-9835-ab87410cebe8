using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;

namespace KPA.Services.QuestPDF.Services;

public interface IAzureBlobService
{
    Task<string> UploadPdfAsync(byte[] pdfData, string fileName, string containerName, string connectionString);
}

public class AzureBlobService : IAzureBlobService
{
    public async Task<string> UploadPdfAsync(byte[] pdfData, string fileName, string containerName, string connectionString)
    {
        try
        {
            var blobServiceClient = new BlobServiceClient(connectionString);
            var containerClient = blobServiceClient.GetBlobContainerClient(containerName);
            
            // Create container if it doesn't exist
            await containerClient.CreateIfNotExistsAsync(PublicAccessType.Blob);
            
            // Generate unique filename
            var uniqueFileName = $"{Path.GetFileNameWithoutExtension(fileName)}_{DateTime.UtcNow:yyyyMMddHHmmss}.pdf";
            var blobClient = containerClient.GetBlobClient(uniqueFileName);
            
            // Upload the PDF
            using var stream = new MemoryStream(pdfData);
            await blobClient.UploadAsync(stream, overwrite: true);

            // Set content type
            await blobClient.SetHttpHeadersAsync(new BlobHttpHeaders { ContentType = "application/pdf" });
            
            return blobClient.Uri.ToString();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to upload PDF to Azure Blob Storage: {ex.Message}", ex);
        }
    }
}
