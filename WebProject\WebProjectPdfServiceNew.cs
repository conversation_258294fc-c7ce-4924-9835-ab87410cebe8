using KPA.Services.QuestPDF.Shared;

namespace KPA.Services.QuestPDF.WebProject;

/// <summary>
/// WebProject PDF generator service using the shared base class
/// </summary>
public class WebProjectPdfServiceNew : BasePdfService<WebProjectPdfRequest, WebProjectPdfDocumentNew>
{
    public WebProjectPdfServiceNew(HttpClient httpClient) : base(httpClient)
    {
    }

    protected override WebProjectPdfDocumentNew CreateDocument(WebProjectPdfRequest request)
    {
        return new WebProjectPdfDocumentNew(request, _httpClient);
    }

    protected override string GetTitleFromRequest(WebProjectPdfRequest request)
    {
        return request.Title;
    }

    protected override string GetFileNameSuffix()
    {
        return "project";
    }

    protected override string GetDefaultFileName()
    {
        return "webproject";
    }
}
