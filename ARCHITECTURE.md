# PDF Generation Architecture

## Overview

This project uses a **shared base class architecture** to avoid code duplication between different PDF document types (WebProject, WebAboutUs, etc.). The architecture provides:

- ✅ **Shared image handling and caching**
- ✅ **A3 paper size optimization**
- ✅ **Consistent compression settings**
- ✅ **Reusable UI components**
- ✅ **Type-safe generics**

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                    Shared Components                        │
├─────────────────────────────────────────────────────────────┤
│  BasePdfDocument<TRequest>     │  BasePdfService<TRequest>   │
│  - Image caching               │  - PDF generation           │
│  - A3 optimization             │  - Companion preview        │
│  - Orientation detection       │  - File naming              │
│  - Shared UI components        │  - Error handling           │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                 Specific Implementations                    │
├─────────────────────────────────────────────────────────────┤
│  WebProjectPdfDocument         │  WebAboutUsPdfDocument      │
│  - Project-specific layout     │  - About Us layout          │
│  - Project details rendering   │  - Company info rendering   │
│  - Brand colors (#f47920)      │  - Brand colors (#2E86AB)   │
└─────────────────────────────────────────────────────────────┘
```

## Folder Structure

```
├── Shared/
│   ├── BasePdfDocument.cs      # Base document with shared functionality
│   ├── BasePdfService.cs       # Base service with shared methods
│   └── A3PaperConfig.cs        # A3 paper size configuration
├── WebProject/
│   ├── WebProjectPdfRequest.cs     # Project-specific request model
│   ├── WebProjectPdfDocument.cs    # Legacy implementation
│   ├── WebProjectPdfDocumentNew.cs # New implementation using base class
│   ├── WebProjectPdfService.cs     # Legacy service
│   └── WebProjectPdfServiceNew.cs  # New service using base class
└── WebAboutUs/
    ├── WebAboutUsPdfRequest.cs     # About Us request model
    ├── WebAboutUsPdfDocument.cs    # About Us document implementation
    ├── WebAboutUsPdfService.cs     # Legacy service
    └── WebAboutUsPdfServiceNew.cs  # New service using base class
```

## Key Benefits

### 1. **No Code Duplication**
- Image downloading, caching, and optimization logic is shared
- A3 paper size settings are centralized
- Common UI components are reusable

### 2. **Type Safety**
- Generic base classes ensure type safety
- Each document type works with its specific request model
- Compile-time validation of relationships

### 3. **Easy Extension**
- Adding new PDF types requires minimal code
- Just inherit from base classes and implement abstract methods
- Automatic A3 optimization and image caching

### 4. **Consistent Performance**
- All documents use the same optimized image caching
- Consistent compression settings across all PDF types
- Single download per image regardless of document type

## Usage Examples

### Creating a New PDF Document Type

```csharp
// 1. Create request model
public class MyPdfRequest : PdfRequest
{
    public string Title { get; set; }
    public List<MySlide> Slides { get; set; }
}

// 2. Create document class
public class MyPdfDocument : BasePdfDocument<MyPdfRequest>
{
    public MyPdfDocument(MyPdfRequest request, HttpClient httpClient) 
        : base(request, httpClient) { }

    protected override IEnumerable<string> GetImageUrls()
    {
        return _request.Slides.Select(s => s.Url);
    }

    public override void Compose(IDocumentContainer container)
    {
        // Your specific layout implementation
    }
}

// 3. Create service class
public class MyPdfService : BasePdfService<MyPdfRequest, MyPdfDocument>
{
    public MyPdfService(HttpClient httpClient) : base(httpClient) { }

    protected override MyPdfDocument CreateDocument(MyPdfRequest request)
    {
        return new MyPdfDocument(request, _httpClient);
    }

    protected override string GetTitleFromRequest(MyPdfRequest request) => request.Title;
    protected override string GetFileNameSuffix() => "my-pdf";
    protected override string GetDefaultFileName() => "my-document";
}
```

## Shared Features

### Image Handling
- **Automatic caching**: Images downloaded once and reused
- **Orientation detection**: Automatic portrait/landscape detection
- **A3 optimization**: Images analyzed for A3 paper size compatibility
- **Error handling**: Graceful fallbacks for failed downloads

### A3 Paper Configuration
```csharp
public static class A3PaperConfig
{
    public const int DPI_STANDARD = 150;  // Good quality, smaller files
    public const int DPI_HIGH = 300;      // High quality, larger files
    
    // Optimal pixel dimensions for A3 paper
    public const int WIDTH_300DPI = 3508;   // 297mm at 300 DPI  
    public const int HEIGHT_300DPI = 4960;  // 420mm at 300 DPI
    
    public static readonly ImageCompressionQuality DefaultCompression = Medium;
}
```

### Shared UI Components
- `ComposeImage()`: Renders cached images with fallbacks
- `ComposeImageFallback()`: Shows error messages when images fail
- `GetImageOrientation()`: Returns cached orientation data

## Migration Path

### Current State
- ✅ **WebProject**: Both legacy and new implementations available
- ✅ **WebAboutUs**: New implementation created using shared base

### Recommended Approach
1. **Test new implementations** alongside legacy versions
2. **Gradually migrate** endpoints to use new services
3. **Remove legacy code** once new implementations are validated
4. **Add new PDF types** using the shared architecture

## Performance Improvements

### Before (Legacy)
- Each image downloaded multiple times (orientation + display)
- Duplicate image optimization code
- Inconsistent compression settings

### After (Shared Architecture)
- ✅ **50% fewer HTTP requests** - each image downloaded once
- ✅ **Consistent A3 optimization** across all document types
- ✅ **Faster PDF generation** - no waiting for downloads during layout
- ✅ **Smaller codebase** - shared functionality eliminates duplication

## Testing

Use the provided HTTP test files to test both document types:

```http
### WebProject PDF
POST {{host}}/api/webproject/pdf/generate-and-preview

### WebAboutUs PDF  
POST {{host}}/api/webaboutus/pdf/generate-and-preview
```

Both will automatically:
1. Download and cache all images
2. Detect image orientations
3. Apply A3 paper size optimization
4. Generate PDFs with consistent quality settings
