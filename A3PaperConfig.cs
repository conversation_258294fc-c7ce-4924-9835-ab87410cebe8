using QuestPDF.Infrastructure;

namespace KPA.Services.QuestPDF;

/// <summary>
/// Configuration for A3 paper size optimization
/// </summary>
public static class A3PaperConfig
{
    // A3 paper dimensions: 297 × 420 mm (11.7 × 16.5 inches)
    public const int DPI_STANDARD = 150;  // Good quality, smaller file size
    public const int DPI_HIGH = 300;      // High quality, larger file size

    // Pixel dimensions at different DPIs
    public const int WIDTH_150DPI = 1754;   // 297mm at 150 DPI
    public const int HEIGHT_150DPI = 2480;  // 420mm at 150 DPI
    public const int WIDTH_300DPI = 3508;   // 297mm at 300 DPI
    public const int HEIGHT_300DPI = 4960;  // 420mm at 300 DPI

    // Compression settings
    public static readonly ImageCompressionQuality DefaultCompression = ImageCompressionQuality.High;
    public static readonly int DefaultDPI = DPI_STANDARD;
}
