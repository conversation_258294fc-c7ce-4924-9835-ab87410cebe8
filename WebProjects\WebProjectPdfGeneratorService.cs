using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using QuestPDF.Companion;

namespace KPA.Services.QuestPDF.WebProjects;

/// <summary>
/// Simple WebProject PDF generator service
/// </summary>
public class WebProjectPdfService
{
    private readonly HttpClient _httpClient;

    static WebProjectPdfService()
    {
        global::QuestPDF.Settings.License = LicenseType.Community;
    }

    public WebProjectPdfService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    /// <summary>
    /// Generate PDF as byte array
    /// </summary>
    public byte[] GeneratePdf(WebProjectPdfRequest request)
    {
        var document = new WebProjectPdfDocument(request, _httpClient);
        return document.GeneratePdf();
    }

    /// <summary>
    /// Show PDF in QuestPDF Companion app
    /// </summary>
    public void ShowInCompanion(WebProjectPdfRequest request)
    {
        var document = new WebProjectPdfDocument(request, _httpClient);
        document.ShowInCompanion();
    }

    /// <summary>
    /// Generate filename for the PDF
    /// </summary>
    public string GenerateFileName(WebProjectPdfRequest request)
    {
        if (!string.IsNullOrEmpty(request.CustomFileName))
            return request.CustomFileName;

        var sanitizedTitle = SanitizeFileName(request.Title);
        var year = !string.IsNullOrEmpty(request.Year) ? request.Year : DateTime.Now.Year.ToString();
        return $"{sanitizedTitle}_{year}_project.pdf";
    }

    private static string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
        return string.IsNullOrEmpty(sanitized) ? "webproject" : sanitized.Replace(" ", "_").ToLowerInvariant();
    }
}

/// <summary>
/// QuestPDF document for WebProject
/// </summary>
public class WebProjectPdfDocument : IDocument
{
    private readonly WebProjectPdfRequest _project;
    private readonly HttpClient _httpClient;

    public WebProjectPdfDocument(WebProjectPdfRequest project, HttpClient httpClient)
    {
        _project = project;
        _httpClient = httpClient;
    }

    public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

    public DocumentSettings GetSettings() => DocumentSettings.Default;

    public void Compose(IDocumentContainer container)
    {
        container
            .Page(page =>
            {
                page.Size(PageSizes.A3.Landscape());
                page.Margin(20, Unit.Millimetre);
                page.DefaultTextStyle(x => x.FontSize(14).FontFamily(Fonts.Calibri));

                page.Content()
                // .Border(1).BorderColor(Colors.Orange.Accent1)
                .Column(mainColumn =>
                {
                    var validImages = _project.Slides.Select(s => s.Url).ToList();

                    if (validImages.Count == 1)
                    {
                        mainColumn.Item().Element(ComposeSingleImagePage);
                    }
                    else
                    {
                        //divide page in 2 equal columns
                        mainColumn.Item().Row(row =>
                              {

                                  row.RelativeItem(1).Element(ComposeProjectDetails);

                                  row.RelativeItem(1)
                                      .Background(Colors.Grey.Lighten2)
                                      .Padding(10);
                              });

                    }

                    // Project details section (always at top)
                    // mainColumn.Item().Element(ComposeProjectDetails);

                    // // Images section - layout depends on number of images
                    // if (_project.Slides.Any())
                    // {
                    //     mainColumn.Item().PaddingTop(15).Element(ComposeImagesSection);
                    // }
                });
            });
    }

    private void ComposeSingleImagePage(IContainer container)
    {
        try
        {
            var validImages = _project.Slides.OrderBy(s => s.OrderFlag).Select(s => s.Url).ToList();

            if (validImages.Count > 0)
            {
                var imageUrl = validImages.First();
                var imageOrientation = GetImageOrientation(imageUrl);

                if (imageOrientation == ImageOrientation.Portrait)
                {
                    // Vertical image: project details on top, image on bottom
                    container.Column(column =>
                    {
                        column.Item().Element(ComposeProjectDetails);
                        column.Item().PaddingTop(15).Element(ComposeMainImage);
                    });
                }
                else
                {
                    // Horizontal/Square image: project details on left, image on right
                    container.Row(row =>
                    {
                        row.RelativeItem(1).Element(ComposeProjectDetails);
                        row.RelativeItem(2).PaddingLeft(15).Element(ComposeMainImage);
                    });
                }
            }
            else
            {
                // No images available, just show project details
                container.Element(ComposeProjectDetails);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in ComposeSingleImagePage: {ex.Message}");
            // Fallback to default horizontal layout
            container.Row(row =>
            {
                row.RelativeItem(1).Element(ComposeProjectDetails);
                row.RelativeItem(2).PaddingLeft(15).Element(ComposeMainImage);
            });
        }
    }

    private void ComposeProjectDetails(IContainer container)
    {
        container
        .Padding(10)
        .Background(Colors.Blue.Lighten5)
        .Column(column =>
            {
                // Title
                column.Item()
                .Text(_project.Title)
                    .FontSize(18)
                    .Bold()
                    .FontColor(Colors.Orange.Accent3);

                // Details table
                column.Item()
                .PaddingTop(5)
                .Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.ConstantColumn(60);
                        columns.RelativeColumn();
                    });

                    // Client
                    if (!string.IsNullOrEmpty(_project.Client))
                    {
                        table.Cell().Text("Client:").SemiBold().FontSize(12).FontColor(Colors.Grey.Darken2);
                        table.Cell().Text(_project.Client);
                    }

                    // Location
                    if (!string.IsNullOrEmpty(_project.Location))
                    {
                        table.Cell().Text("Location:").SemiBold().FontSize(12).FontColor(Colors.Grey.Darken2);
                        table.Cell().Text(_project.Location);
                    }

                    // Typology
                    if (!string.IsNullOrEmpty(_project.Typology))
                    {
                        table.Cell().Text("Typology:").SemiBold().FontSize(12).FontColor(Colors.Grey.Darken2);
                        table.Cell().Text(_project.Typology);
                    }

                    // Area
                    if (!string.IsNullOrEmpty(_project.Area))
                    {
                        table.Cell().Text("Area:").SemiBold().FontSize(12).FontColor(Colors.Grey.Darken2);
                        table.Cell().Text(_project.Area);
                    }

                    // Year
                    if (!string.IsNullOrEmpty(_project.Year))
                    {
                        table.Cell().Text("Year:").SemiBold().FontSize(12).FontColor(Colors.Grey.Darken2);
                        table.Cell().Text(_project.Year);
                    }

                    // Status
                    if (!string.IsNullOrEmpty(_project.Status))
                    {
                        table.Cell().Text("Status:").SemiBold().FontSize(12).FontColor(Colors.Grey.Darken2);
                        table.Cell().Text(_project.Status);
                    }
                });
            });
    }

    private void ComposeMainImage(IContainer container)
    {
        try
        {
            var validImages = _project.Slides.OrderBy(s => s.OrderFlag).Select(s => s.Url).ToList();

            if (validImages.Count > 0)
            {
                var imageUrl = validImages.First();
                var imageBytes = DownloadImageAsync(imageUrl).GetAwaiter().GetResult();

                if (imageBytes != null)
                {
                    container
                        .Padding(10)
                        .Background(Colors.Purple.Lighten4)
                        .Image(imageBytes)
                        .FitArea(); // This will use all available width and height
                }
                else
                {
                    ComposeImageFallback(container, "Failed to download image");
                }
            }
            else
            {
                // Fallback when no images are available
                ComposeImageFallback(container, "No image available");
            }
        }
        catch (Exception ex)
        {
            // Log the error for debugging
            Console.WriteLine($"Error loading full width image: {ex.Message}");
            ComposeImageFallback(container, "Image failed to load");
        }
    }

    private async Task<byte[]?> DownloadImageAsync(string imageUrl)
    {
        try
        {
            if (string.IsNullOrEmpty(imageUrl))
                return null;

            // Check if it's a web URL
            if (imageUrl.StartsWith("http://") || imageUrl.StartsWith("https://"))
            {
                var response = await _httpClient.GetAsync(imageUrl);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadAsByteArrayAsync();
                }
                else
                {
                    Console.WriteLine($"Failed to download image: {response.StatusCode} - {imageUrl}");
                    return null;
                }
            }
            else
            {
                // Local file path
                if (File.Exists(imageUrl))
                {
                    return await File.ReadAllBytesAsync(imageUrl);
                }
                else
                {
                    Console.WriteLine($"Local file not found: {imageUrl}");
                    return null;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error downloading image {imageUrl}: {ex.Message}");
            return null;
        }
    }

    private static void ComposeImageFallback(IContainer container, string message)
    {
        container
            .Padding(10)
            .Background(Colors.Grey.Lighten3)
            .AlignCenter()
            .AlignMiddle()
            .Text(message)
            .FontSize(16)
            .FontColor(Colors.Grey.Darken1);
    }

    private enum ImageOrientation
    {
        Portrait,
        Landscape,
        Square
    }

    private ImageOrientation GetImageOrientation(string imageUrl)
    {
        try
        {
            var imageBytes = DownloadImageAsync(imageUrl).GetAwaiter().GetResult();

            if (imageBytes != null)
            {
                var dimensions = GetImageDimensions(imageBytes);

                if (dimensions.Width > 0 && dimensions.Height > 0)
                {
                    if (dimensions.Width > dimensions.Height)
                    {
                        return ImageOrientation.Landscape;
                    }
                    else if (dimensions.Height > dimensions.Width)
                    {
                        return ImageOrientation.Portrait;
                    }
                    else
                    {
                        return ImageOrientation.Square;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error getting image orientation for {imageUrl}: {ex.Message}");
        }

        // Default to landscape if we can't determine orientation
        return ImageOrientation.Landscape;
    }

    private (int Width, int Height) GetImageDimensions(byte[] imageBytes)
    {
        try
        {
            // Simple JPEG dimension detection
            if (imageBytes.Length > 10 && imageBytes[0] == 0xFF && imageBytes[1] == 0xD8)
            {
                return GetJpegDimensions(imageBytes);
            }

            // Simple PNG dimension detection
            if (imageBytes.Length > 24 &&
                imageBytes[0] == 0x89 && imageBytes[1] == 0x50 &&
                imageBytes[2] == 0x4E && imageBytes[3] == 0x47)
            {
                return GetPngDimensions(imageBytes);
            }

            // For other formats or if detection fails, assume landscape
            return (1920, 1080); // Default landscape dimensions
        }
        catch
        {
            return (1920, 1080); // Default landscape dimensions
        }
    }

    private (int Width, int Height) GetJpegDimensions(byte[] imageBytes)
    {
        try
        {
            for (int i = 2; i < imageBytes.Length - 8; i++)
            {
                if (imageBytes[i] == 0xFF && (imageBytes[i + 1] == 0xC0 || imageBytes[i + 1] == 0xC2))
                {
                    int height = (imageBytes[i + 5] << 8) | imageBytes[i + 6];
                    int width = (imageBytes[i + 7] << 8) | imageBytes[i + 8];
                    return (width, height);
                }
            }
        }
        catch { }

        return (1920, 1080); // Default if parsing fails
    }

    private (int Width, int Height) GetPngDimensions(byte[] imageBytes)
    {
        try
        {
            if (imageBytes.Length >= 24)
            {
                int width = (imageBytes[16] << 24) | (imageBytes[17] << 16) | (imageBytes[18] << 8) | imageBytes[19];
                int height = (imageBytes[20] << 24) | (imageBytes[21] << 16) | (imageBytes[22] << 8) | imageBytes[23];
                return (width, height);
            }
        }
        catch { }

        return (1920, 1080); // Default if parsing fails
    }

}
