using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using QuestPDF.Companion;

namespace KPA.Services.QuestPDF.Services;

public class WebProjectPdfGeneratorService
{
    static WebProjectPdfGeneratorService()
    {
        global::QuestPDF.Settings.License = LicenseType.Community;
    }

    public byte[] GeneratePdf(WebProject project)
    {
        var document = new ProjectPdfDocument(project);
        return document.GeneratePdf();
    }

    public void ShowInCompanion(WebProject project)
    {
        var document = new ProjectPdfDocument(project);
        document.ShowInCompanion();
    }
}

public class ProjectPdfDocument : IDocument
{
    private readonly WebProject _project;

    public ProjectPdfDocument(WebProject project)
    {
        _project = project;
    }

    public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

    public DocumentSettings GetSettings() => DocumentSettings.Default;

    public void Compose(IDocumentContainer container)
    {
        container
            .Page(page =>
            {
                page.Size(PageSizes.A4.Landscape());
                page.Margin(20, Unit.Millimetre);
                page.DefaultTextStyle(x => x.FontSize(10).FontFamily(Fonts.Arial));

                page.Content().Column(mainColumn =>
                {
                    // Project details section (always at top)
                    mainColumn.Item().Element(ComposeProjectDetails);

                    // Images section - layout depends on number of images
                    if (_project.ImageUrls.Any())
                    {
                        mainColumn.Item().PaddingTop(15).Element(ComposeImagesSection);
                    }
                });
            });
    }

    private void ComposeProjectDetails(IContainer container)
    {
        container.Row(row =>
        {
            // Left side - Title and Description
            row.RelativeItem(2).Column(leftColumn =>
            {
                // Title
                leftColumn.Item().Text(_project.Title)
                    .FontSize(18)
                    .Bold()
                    .FontColor(Colors.Black);

                // Description
                if (!string.IsNullOrEmpty(_project.Description))
                {
                    leftColumn.Item().PaddingTop(8).Text(_project.Description)
                        .FontSize(11)
                        .LineHeight(1.3f)
                        .FontColor(Colors.Grey.Darken1);
                }
            });

            // Right side - Project Details
            row.RelativeItem(1).PaddingLeft(20).Column(rightColumn =>
            {
                rightColumn.Item().Text("Project Details")
                    .FontSize(12)
                    .SemiBold()
                    .FontColor(Colors.Grey.Darken2);

                // Details table
                rightColumn.Item().PaddingTop(5).Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.ConstantColumn(60);
                        columns.RelativeColumn();
                    });

                    // Client
                    if (!string.IsNullOrEmpty(_project.Client))
                    {
                        table.Cell().Text("Client:").SemiBold().FontSize(9);
                        table.Cell().Text(_project.Client).FontSize(9);
                    }

                    // Location
                    if (!string.IsNullOrEmpty(_project.Location))
                    {
                        table.Cell().Text("Location:").SemiBold().FontSize(9);
                        table.Cell().Text(_project.Location).FontSize(9);
                    }

                    // Project Type
                    if (!string.IsNullOrEmpty(_project.ProjectType))
                    {
                        table.Cell().Text("Type:").SemiBold().FontSize(9);
                        table.Cell().Text(_project.ProjectType).FontSize(9);
                    }

                    // Date
                    if (_project.ProjectDate != default)
                    {
                        table.Cell().Text("Date:").SemiBold().FontSize(9);
                        table.Cell().Text(_project.ProjectDate.ToString("MMMM yyyy")).FontSize(9);
                    }
                });
            });
        });
    }

    private void ComposeImagesSection(IContainer container)
    {
        var validImages = _project.ImageUrls.Where(IsValidImageUrl).ToList();

        if (validImages.Count == 1)
        {
            // Single image spans full width
            ComposeFullWidthImage(container, validImages.First());
        }
        else if (validImages.Count > 1)
        {
            // Multiple images: main on left, others on right
            container.Row(row =>
            {
                // Left column - Main image
                row.RelativeItem(1).Element(container => ComposeMainImage(container, validImages.First()));

                // Right column - Additional images
                row.RelativeItem(1).PaddingLeft(15).Element(container =>
                    ComposeAdditionalImages(container, validImages.Skip(1).ToList()));
            });
        }
    }

    private void ComposeFullWidthImage(IContainer container, string imageUrl)
    {
        try
        {
            container.Column(column =>
            {
                column.Item().Text("Project Image")
                    .FontSize(12)
                    .SemiBold()
                    .FontColor(Colors.Grey.Darken2);

                column.Item().PaddingTop(8).Height(300).Image(imageUrl).FitArea();
            });
        }
        catch
        {
            container.Height(300).Background(Colors.Grey.Lighten3)
                .AlignCenter().AlignMiddle()
                .Text("Image not available").FontSize(12).FontColor(Colors.Grey.Darken1);
        }
    }

    private void ComposeMainImage(IContainer container, string imageUrl)
    {
        try
        {
            container.Column(column =>
            {
                column.Item().Text("Main Image")
                    .FontSize(12)
                    .SemiBold()
                    .FontColor(Colors.Grey.Darken2);

                column.Item().PaddingTop(8).Height(250).Image(imageUrl).FitArea();
            });
        }
        catch
        {
            container.Height(250).Background(Colors.Grey.Lighten3)
                .AlignCenter().AlignMiddle()
                .Text("Image not available").FontSize(10).FontColor(Colors.Grey.Darken1);
        }
    }

    private void ComposeAdditionalImages(IContainer container, List<string> imageUrls)
    {
        container.Column(column =>
        {
            column.Item().Text("Additional Images")
                .FontSize(12)
                .SemiBold()
                .FontColor(Colors.Grey.Darken2);

            var validImages = imageUrls.Where(IsValidImageUrl).Take(4).ToList();

            if (validImages.Count <= 2)
            {
                // 1-2 images: stack vertically
                for (int i = 0; i < validImages.Count; i++)
                {
                    var topPadding = i == 0 ? 8 : 10;
                    try
                    {
                        column.Item().PaddingTop(topPadding).Height(120).Image(validImages[i]).FitArea();
                    }
                    catch
                    {
                        column.Item().PaddingTop(topPadding).Height(120).Background(Colors.Grey.Lighten3)
                            .AlignCenter().AlignMiddle()
                            .Text("Image not available").FontSize(9).FontColor(Colors.Grey.Darken1);
                    }
                }
            }
            else
            {
                // 3-4 images: arrange in 2x2 grid
                for (int i = 0; i < validImages.Count; i += 2)
                {
                    var topPadding = i == 0 ? 8 : 10;

                    if (i + 1 < validImages.Count)
                    {
                        // Two images side by side
                        column.Item().PaddingTop(topPadding).Row(row =>
                        {
                            row.RelativeItem(1).Element(container =>
                                ComposeSmallImage(container, validImages[i], 80));
                            row.RelativeItem(1).PaddingLeft(5).Element(container =>
                                ComposeSmallImage(container, validImages[i + 1], 80));
                        });
                    }
                    else
                    {
                        // Single image (odd number)
                        try
                        {
                            column.Item().PaddingTop(topPadding).Height(80).Image(validImages[i]).FitArea();
                        }
                        catch
                        {
                            column.Item().PaddingTop(topPadding).Height(80).Background(Colors.Grey.Lighten3)
                                .AlignCenter().AlignMiddle()
                                .Text("Image not available").FontSize(9).FontColor(Colors.Grey.Darken1);
                        }
                    }
                }
            }
        });
    }

    private void ComposeSmallImage(IContainer container, string imageUrl, int height)
    {
        try
        {
            container.Height(height).Image(imageUrl).FitArea();
        }
        catch
        {
            container.Height(height).Background(Colors.Grey.Lighten3)
                .AlignCenter().AlignMiddle()
                .Text("Image not available").FontSize(8).FontColor(Colors.Grey.Darken1);
        }
    }

    private static bool IsValidImageUrl(string url)
    {
        if (string.IsNullOrEmpty(url)) return false;
        
        try
        {
            var uri = new Uri(url);
            var extension = Path.GetExtension(uri.LocalPath).ToLowerInvariant();
            return new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" }.Contains(extension);
        }
        catch
        {
            return false;
        }
    }
}
