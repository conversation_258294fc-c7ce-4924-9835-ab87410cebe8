using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using QuestPDF.Companion;

namespace KPA.Services.QuestPDF.WebProjects;

/// <summary>
/// Simple WebProject PDF generator service
/// </summary>
public class WebProjectPdfService
{
    static WebProjectPdfService()
    {
        global::QuestPDF.Settings.License = LicenseType.Community;
    }

    /// <summary>
    /// Generate PDF as byte array
    /// </summary>
    public byte[] GeneratePdf(WebProjectPdfRequest request)
    {
        var document = new WebProjectPdfDocument(request);
        return document.GeneratePdf();
    }

    /// <summary>
    /// Show PDF in QuestPDF Companion app
    /// </summary>
    public void ShowInCompanion(WebProjectPdfRequest request)
    {
        var document = new WebProjectPdfDocument(request);
        document.ShowInCompanion();
    }

    /// <summary>
    /// Generate filename for the PDF
    /// </summary>
    public string GenerateFileName(WebProjectPdfRequest request)
    {
        if (!string.IsNullOrEmpty(request.CustomFileName))
            return request.CustomFileName;

        var sanitizedTitle = SanitizeFileName(request.Title);
        var year = !string.IsNullOrEmpty(request.Year) ? request.Year : DateTime.Now.Year.ToString();
        return $"{sanitizedTitle}_{year}_project.pdf";
    }

    private static string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
        return string.IsNullOrEmpty(sanitized) ? "webproject" : sanitized.Replace(" ", "_").ToLowerInvariant();
    }
}

/// <summary>
/// QuestPDF document for WebProject
/// </summary>
public class WebProjectPdfDocument : IDocument
{
    private readonly WebProjectPdfRequest _project;

    public WebProjectPdfDocument(WebProjectPdfRequest project)
    {
        _project = project;
    }

    public DocumentMetadata GetMetadata() => DocumentMetadata.Default;

    public DocumentSettings GetSettings() => DocumentSettings.Default;

    public void Compose(IDocumentContainer container)
    {
        container
            .Page(page =>
            {
                page.Size(PageSizes.A3.Landscape());
                page.Margin(20, Unit.Millimetre);
                page.DefaultTextStyle(x => x.FontSize(14).FontFamily(Fonts.Calibri));

                page.Content().Border(1).BorderColor(Colors.Orange.Accent1).Column(mainColumn =>
                {
                    var validImages = _project.Slides.Select(s => s.Url).ToList();

                    if (validImages.Count == 1)
                    {
                        mainColumn.Item()
                        .Border(1)
                        .BorderColor(Colors.Orange.Accent1)
                             .Element(ComposeProjectDetails);

                        mainColumn.Item()
                        .Border(1)
                        .BorderColor(Colors.Orange.Accent1)
                        .Element(ComposeFullWidthImage);

                    }
                    else
                    {
                        //divide page in 2 equal columns
                        mainColumn.Item().Row(row =>
                              {

                                  row.RelativeItem(1).Element(ComposeProjectDetails);

                                  row.RelativeItem(1)
                                      .Background(Colors.Grey.Lighten2)
                                      .Padding(10);
                              });

                    }

                    // Project details section (always at top)
                    // mainColumn.Item().Element(ComposeProjectDetails);

                    // // Images section - layout depends on number of images
                    // if (_project.Slides.Any())
                    // {
                    //     mainColumn.Item().PaddingTop(15).Element(ComposeImagesSection);
                    // }
                });
            });
    }

    private void ComposeProjectDetails(IContainer container)
    {
        container
        .Padding(10)
        .Background(Colors.Blue.Lighten5)
        .Column(column =>
            {
                // Title
                column.Item()
                .Text(_project.Title)
                    .FontSize(18)
                    .Bold()
                    .FontColor(Colors.Orange.Accent3);

                // Details table
                column.Item()
                .PaddingTop(5)
                .Table(table =>
                {
                    table.ColumnsDefinition(columns =>
                    {
                        columns.ConstantColumn(60);
                        columns.RelativeColumn();
                    });

                    // Client
                    if (!string.IsNullOrEmpty(_project.Client))
                    {
                        table.Cell().Text("Client:").SemiBold().FontSize(12).FontColor(Colors.Grey.Darken2);
                        table.Cell().Text(_project.Client);
                    }

                    // Location
                    if (!string.IsNullOrEmpty(_project.Location))
                    {
                        table.Cell().Text("Location:").SemiBold().FontSize(12).FontColor(Colors.Grey.Darken2);
                        table.Cell().Text(_project.Location);
                    }

                    // Typology
                    if (!string.IsNullOrEmpty(_project.Typology))
                    {
                        table.Cell().Text("Typology:").SemiBold().FontSize(12).FontColor(Colors.Grey.Darken2);
                        table.Cell().Text(_project.Typology);
                    }

                    // Area
                    if (!string.IsNullOrEmpty(_project.Area))
                    {
                        table.Cell().Text("Area:").SemiBold().FontSize(12).FontColor(Colors.Grey.Darken2);
                        table.Cell().Text(_project.Area);
                    }

                    // Year
                    if (!string.IsNullOrEmpty(_project.Year))
                    {
                        table.Cell().Text("Year:").SemiBold().FontSize(12).FontColor(Colors.Grey.Darken2);
                        table.Cell().Text(_project.Year);
                    }

                    // Status
                    if (!string.IsNullOrEmpty(_project.Status))
                    {
                        table.Cell().Text("Status:").SemiBold().FontSize(12).FontColor(Colors.Grey.Darken2);
                        table.Cell().Text(_project.Status);
                    }
                });
            });
    }

    private void ComposeImagesSection(IContainer container)
    {
        var validImages = _project.Slides.Select(s => s.Url).ToList();

        if (validImages.Count == 1)
        {
            // Single image spans full width
            ComposeFullWidthImage(container);
        }
        else if (validImages.Count > 1)
        {
            // Multiple images: main on left, others on right
            container.Row(row =>
            {
                // Left column - Main image
                row.RelativeItem(1).Element(container => ComposeMainImage(container, validImages.First()));

                // Right column - Additional images
                row.RelativeItem(1).PaddingLeft(15).Element(container =>
                    ComposeAdditionalImages(container, validImages.Skip(1).ToList()));
            });
        }
    }

    private void ComposeFullWidthImage(IContainer container)
    {
        try
        {
            var validImages = _project.Slides.OrderBy(s => s.OrderFlag).Select(s => s.Url).ToList();

            container.Padding(10)
            .Background(Colors.Purple.Lighten4)
            .Column(column =>
            {
                column.Item().PaddingBottom(10).Text($"Full Width Image : {validImages.First()}").Bold();
                column.Item()
                    .Background(Colors.Purple.Lighten4)
                .Image(validImages.First())
                .FitArea();
            });
        }
        catch
        {
            // container
            //     .Height(300)
            //     .Background(Colors.Grey.Lighten3)
            //     .AlignCenter()
            //     .AlignMiddle()
            //     .Text("Image not available")
            //     .FontSize(12)
            //     .FontColor(Colors.Grey.Darken1);
        }
    }

    private static void ComposeMainImage(IContainer container, string imageUrl)
    {
        try
        {
            container.Column(column =>
            {
                column.Item().Text("Main Image")
                    .FontSize(12)
                    .SemiBold()
                    .FontColor(Colors.Grey.Darken2);

                column.Item().PaddingTop(8).Height(250).Image(imageUrl).FitArea();
            });
        }
        catch
        {
            container
                .Height(250)
                .Background(Colors.Grey.Lighten3)
                .AlignCenter()
                .AlignMiddle()
                .Text("Image not available")
                .FontSize(10)
                .FontColor(Colors.Grey.Darken1);
        }
    }

    private static void ComposeAdditionalImages(IContainer container, List<string> imageUrls)
    {
        container.Column(column =>
        {
            column.Item().Text("Additional Images")
                .FontSize(12)
                .SemiBold()
                .FontColor(Colors.Grey.Darken2);

            var validImages = imageUrls.Take(4).ToList();

            if (validImages.Count <= 2)
            {
                // 1-2 images: stack vertically
                for (int i = 0; i < validImages.Count; i++)
                {
                    var topPadding = i == 0 ? 8 : 10;
                    try
                    {
                        column.Item().PaddingTop(topPadding).Height(120).Image(validImages[i]).FitArea();
                    }
                    catch
                    {
                        column.Item().PaddingTop(topPadding)
                            .Height(120)
                            .Background(Colors.Grey.Lighten3)
                            .AlignCenter()
                            .AlignMiddle()
                            .Text("Image not available")
                            .FontSize(9)
                            .FontColor(Colors.Grey.Darken1);
                    }
                }
            }
            else
            {
                // 3-4 images: arrange in 2x2 grid
                for (int i = 0; i < validImages.Count; i += 2)
                {
                    var topPadding = i == 0 ? 8 : 10;

                    if (i + 1 < validImages.Count)
                    {
                        // Two images side by side
                        column.Item().PaddingTop(topPadding).Row(row =>
                        {
                            row.RelativeItem(1).Element(container =>
                                ComposeSmallImage(container, validImages[i], 80));
                            row.RelativeItem(1).PaddingLeft(5).Element(container =>
                                ComposeSmallImage(container, validImages[i + 1], 80));
                        });
                    }
                    else
                    {
                        // Single image (odd number)
                        try
                        {
                            column.Item().PaddingTop(topPadding).Height(80).Image(validImages[i]).FitArea();
                        }
                        catch
                        {
                            column.Item().PaddingTop(topPadding)
                                .Height(80)
                                .Background(Colors.Grey.Lighten3)
                                .AlignCenter()
                                .AlignMiddle()
                                .Text("Image not available")
                                .FontSize(9)
                                .FontColor(Colors.Grey.Darken1);
                        }
                    }
                }
            }
        });
    }

    private static void ComposeSmallImage(IContainer container, string imageUrl, int height)
    {
        try
        {
            container.Height(height).Image(imageUrl).FitArea();
        }
        catch
        {
            container
                .Height(height)
                .Background(Colors.Grey.Lighten3)
                .AlignCenter()
                .AlignMiddle()
                .Text("Image not available")
                .FontSize(8)
                .FontColor(Colors.Grey.Darken1);
        }
    }

}
